#include "LEDController.h"
#include "DisplayDriver.h"
#include "FontData.h"

// ==================== 全局变量定义 ====================
TextDisplayState textState = {"", "", 0, 0, 0, false, BT_DIRECTION_HORIZONTAL}; // 全局文本状态
uint8_t currentFontSize = BT_FONT_16x16;                                        // 全局字体大小标志位
ColorState colorState = {
    // 上半屏颜色初始化
    COLOR_WHITE,
    0x0000,
    BT_COLOR_MODE_FIXED,
    BT_COLOR_MODE_FIXED,
    255,
    255,
    255,
    0,
    0,
    0,
    BT_GRADIENT_FIXED,
    // 下半屏颜色初始化
    COLOR_WHITE,
    0x0000,
    BT_COLOR_MODE_FIXED,
    BT_COLOR_MODE_FIXED,
    255,
    255,
    255,
    0,
    0,
    0,
    BT_GRADIENT_FIXED,
    // 全屏颜色初始化（兼容性）
    COLOR_WHITE,
    0x0000,
    BT_COLOR_MODE_FIXED,
    BT_COLOR_MODE_FIXED,
    255,
    255,
    255,
    0,
    0,
    0,
    BT_GRADIENT_FIXED,
    0,
    false,
    // 新增颜色系统初始化
    COLOR_MODE_FIXED,                                    // 默认固定色模式
    {false, nullptr, nullptr, 0, nullptr, nullptr, 0},   // 特定字符颜色系统
    {false, RANDOM_COLOR_OFF, 0, 0, 0, nullptr, 0, 0x00} // 随机颜色系统
}; // 全局颜色状态
EffectState effectState = {
    // 滚动特效初始化
    false, false, BT_EFFECT_FIXED, BT_EFFECT_FIXED, 5, 5, 0, 0,
    // 闪烁特效初始化
    false, false, 5, 5, true, true,
    // 呼吸特效初始化
    false, false, 5, 5, 0.0, 0.0,
    0, false};                                                              // 全局特效状态
BrightnessState brightnessState = {128, false};                             // 全局亮度状态，默认50%亮度
BorderState borderState = {false, 0, COLOR_WHITE, 0, 5, 0, true, 0, false}; // 全局边框状态

// 边框颜色索引映射表（0-6对应红绿蓝黄紫青白）
const uint16_t BORDER_COLORS[7] = {
    COLOR_RED,     // 0
    COLOR_GREEN,   // 1
    COLOR_BLUE,    // 2
    COLOR_YELLOW,  // 3
    COLOR_MAGENTA, // 4
    COLOR_CYAN,    // 5
    COLOR_WHITE    // 6
};

MatrixPanel_I2S_DMA *dma_display = nullptr;

// ==================== 动态点阵数据存储 ====================
uint16_t *dynamic_upper_text = nullptr; // 动态上半屏点阵数据
uint16_t *dynamic_lower_text = nullptr; // 动态下半屏点阵数据
uint16_t *dynamic_full_text = nullptr;  // 动态全屏点阵数据
int dynamic_upper_char_count = 0;       // 动态上半屏字符数
int dynamic_lower_char_count = 0;       // 动态下半屏字符数
int dynamic_full_char_count = 0;        // 动态全屏字符数

// 内存管理辅助函数
void freeDynamicTextData()
{
    if (dynamic_upper_text)
    {
        free(dynamic_upper_text);
        dynamic_upper_text = nullptr;
    }
    if (dynamic_lower_text)
    {
        free(dynamic_lower_text);
        dynamic_lower_text = nullptr;
    }
    if (dynamic_full_text)
    {
        free(dynamic_full_text);
        dynamic_full_text = nullptr;
    }
    dynamic_upper_char_count = 0;
    dynamic_lower_char_count = 0;
    dynamic_full_char_count = 0;
}

// 字体切换时重置所有继承状态
void resetAllStatesOnFontSwitch()
{
    Serial.println("🔄 字体切换：开始重置所有继承状态");

    // 1. 重置文本显示状态
    textState.upperIndex = 0;
    textState.lowerIndex = 0;
    textState.lastSwitchTime = millis();
    textState.needUpdate = true;
    textState.displayDirection = BT_DIRECTION_HORIZONTAL; // 重置为默认正向显示
    Serial.println("✅ 文本显示状态已重置");

    // 2. 重置所有特效状态
    effectState.upperScrollActive = false;
    effectState.lowerScrollActive = false;
    effectState.upperBlinkActive = false;
    effectState.lowerBlinkActive = false;
    effectState.upperBreatheActive = false;
    effectState.lowerBreatheActive = false;
    effectState.upperScrollOffset = 0;
    effectState.lowerScrollOffset = 0;
    effectState.upperBreathePhase = 0.0;
    effectState.lowerBreathePhase = 0.0;
    effectState.upperBlinkVisible = true;
    effectState.lowerBlinkVisible = true;
    effectState.lastEffectTime = 0;
    effectState.needEffectUpdate = false;
    Serial.println("✅ 特效状态已重置");

    // 注意：特效函数中的静态时间变量会在下次调用时自然重置，无需手动处理

    // 3. 重置边框状态
    borderState.active = false;
    borderState.style = 0;
    borderState.fixedColor = COLOR_WHITE;
    borderState.effect = 0;
    borderState.speed = 5;
    borderState.flowOffset = 0;
    borderState.blinkVisible = true;
    borderState.lastUpdateTime = 0;
    borderState.needUpdate = false;
    Serial.println("✅ 边框状态已重置");

    // 4. 重置颜色系统状态
    // 4.1 清除特定字符颜色系统
    if (colorState.specificColor.enabled)
    {
        clearSpecificColors();
        Serial.println("✅ 特定字符颜色系统已清除");
    }

    // 4.2 清除随机颜色系统
    if (colorState.randomColor.enabled)
    {
        clearRandomColors();
        Serial.println("✅ 随机颜色系统已清除");
    }

    // 4.3 重置颜色模式为固定色
    colorState.currentColorMode = COLOR_MODE_FIXED;

    // 4.4 重置上半屏颜色为默认值
    colorState.upperTextColor = COLOR_WHITE;
    colorState.upperBackgroundColor = 0x0000;
    colorState.upperTextMode = BT_COLOR_MODE_FIXED;
    colorState.upperBgMode = BT_COLOR_MODE_FIXED;
    colorState.upperTextR = 255;
    colorState.upperTextG = 255;
    colorState.upperTextB = 255;
    colorState.upperBgR = 0;
    colorState.upperBgG = 0;
    colorState.upperBgB = 0;
    colorState.upperGradientMode = BT_GRADIENT_FIXED;

    // 4.5 重置下半屏颜色为默认值
    colorState.lowerTextColor = COLOR_WHITE;
    colorState.lowerBackgroundColor = 0x0000;
    colorState.lowerTextMode = BT_COLOR_MODE_FIXED;
    colorState.lowerBgMode = BT_COLOR_MODE_FIXED;
    colorState.lowerTextR = 255;
    colorState.lowerTextG = 255;
    colorState.lowerTextB = 255;
    colorState.lowerBgR = 0;
    colorState.lowerBgG = 0;
    colorState.lowerBgB = 0;
    colorState.lowerGradientMode = BT_GRADIENT_FIXED;

    // 4.6 重置全屏颜色为默认值（兼容性）
    colorState.textColor = COLOR_WHITE;
    colorState.backgroundColor = 0x0000;
    colorState.textMode = BT_COLOR_MODE_FIXED;
    colorState.bgMode = BT_COLOR_MODE_FIXED;
    colorState.textR = 255;
    colorState.textG = 255;
    colorState.textB = 255;
    colorState.bgR = 0;
    colorState.bgG = 0;
    colorState.bgB = 0;
    colorState.gradientMode = BT_GRADIENT_FIXED;
    colorState.gradientTime = 0;
    colorState.needColorUpdate = true;
    Serial.println("✅ 颜色状态已重置为默认值");

    // 5. 重置亮度状态（保持当前亮度，但清除更新标志）
    brightnessState.needBrightnessUpdate = false;
    Serial.println("✅ 亮度状态标志已重置");

    // 6. 释放动态内存数据
    freeDynamicTextData();
    Serial.println("✅ 动态内存数据已释放");

    Serial.println("🎉 字体切换：所有继承状态重置完成");
}

// ==================== 硬件初始化 ====================
bool initializeDisplay()
{
    // HUB75自定义引脚配置
    HUB75_I2S_CFG::i2s_pins _pins = {
        25, 26, 27, 14, 12, 13, // R1, G1, B1, R2, G2, B2
        23, 22, 21, 19, -1,     // A, B, C, D, E
        4, 15, 16               // LAT, OE, CLK
    };

    HUB75_I2S_CFG mxconfig(
        SCREEN_WIDTH,  // 模块宽度
        SCREEN_HEIGHT, // 模块高度
        1,             // 面板链长度
        _pins          // 自定义引脚配置
    );

    // 创建显示对象
    dma_display = new MatrixPanel_I2S_DMA(mxconfig);
    if (dma_display == nullptr)
    {
        return false;
    }

    dma_display->begin();
    dma_display->setBrightness8(50); // 亮度0-255
    dma_display->clearScreen();

    return true;
}

// ==================== 文本显示相关函数 ====================
// 在半屏显示文本（支持分组显示和所有特效）
void displayTextOnHalf(int y, bool isUpper)
{
    // 检查闪烁特效是否应该隐藏文本
    bool blinkActive = isUpper ? effectState.upperBlinkActive : effectState.lowerBlinkActive;
    bool blinkVisible = isUpper ? effectState.upperBlinkVisible : effectState.lowerBlinkVisible;

    if (blinkActive && !blinkVisible)
    {
        return; // 闪烁特效激活且当前应该隐藏，直接返回
    }

    // 根据上下半屏选择对应的点阵数据（优先使用动态数据）
    const uint16_t *font_data;
    int total_char_count;

    if (isUpper)
    {
        // 上半屏：优先使用动态数据，否则使用静态数据
        if (dynamic_upper_text && dynamic_upper_char_count > 0)
        {
            font_data = dynamic_upper_text;
            total_char_count = dynamic_upper_char_count;
        }
        else
        {
            font_data = upper_text;
            total_char_count = getUpperTextCharCount();
        }
    }
    else
    {
        // 下半屏：优先使用动态数据，否则使用静态数据
        if (dynamic_lower_text && dynamic_lower_char_count > 0)
        {
            font_data = dynamic_lower_text;
            total_char_count = dynamic_lower_char_count;
        }
        else
        {
            font_data = lower_text;
            total_char_count = getLowerTextCharCount();
        }
    }

    // 检查是否启用滚动特效
    bool scrollActive = isUpper ? effectState.upperScrollActive : effectState.lowerScrollActive;

    // 检查是否使用渐变色
    uint8_t textMode = isUpper ? colorState.upperTextMode : colorState.lowerTextMode;
    uint8_t gradientMode = isUpper ? colorState.upperGradientMode : colorState.lowerGradientMode;
    bool useGradient = (textMode == BT_COLOR_MODE_GRADIENT && gradientMode != BT_GRADIENT_FIXED);

    // 获取基础颜色（用于固定色和呼吸特效）
    uint16_t baseColor = isUpper ? colorState.upperTextColor : colorState.lowerTextColor;
    uint16_t textColor = baseColor;

    // 应用呼吸特效到颜色（仅对固定色有效，渐变色不支持呼吸特效）
    bool breatheActive = isUpper ? effectState.upperBreatheActive : effectState.lowerBreatheActive;
    if (breatheActive && !useGradient)
    {
        float phase = isUpper ? effectState.upperBreathePhase : effectState.lowerBreathePhase;
        float brightness = (sin(phase) + 1.0) / 2.0; // 0.0 到 1.0 的正弦波
        brightness = 0.05 + brightness * 0.95;       // 范围从5%-100%，更强烈的呼吸对比度

        // 提取RGB分量（RGB565格式）
        uint8_t r = (baseColor >> 11) & 0x1F; // 5位红色
        uint8_t g = (baseColor >> 5) & 0x3F;  // 6位绿色
        uint8_t b = baseColor & 0x1F;         // 5位蓝色

        // 应用呼吸亮度
        r = (uint8_t)(r * brightness);
        g = (uint8_t)(g * brightness);
        b = (uint8_t)(b * brightness);

        // 重新组合颜色
        textColor = (r << 11) | (g << 5) | b;
    }

    if (scrollActive)
    {
        // 滚动模式：显示所有字符
        uint8_t scrollType = isUpper ? effectState.upperScrollType : effectState.lowerScrollType;
        int scrollOffset = isUpper ? effectState.upperScrollOffset : effectState.lowerScrollOffset;
        displayScrollingText(font_data, total_char_count, scrollOffset, y, scrollType);
        return;
    }

    // 检查显示方向
    bool isVertical = (textState.displayDirection == BT_DIRECTION_VERTICAL);

    // 非滚动模式：分组显示
    if (isVertical)
    {
        // 竖向显示：每行最多显示4个字符（向左旋转后水平排列）
        const int maxCharsPerLine = SCREEN_WIDTH / CHAR_SPACING_16;

        // 如果字符数不超过4个，直接居中显示
        if (total_char_count <= maxCharsPerLine)
        {
            int display_x = (SCREEN_WIDTH - total_char_count * CHAR_SPACING_16) / 2;
            if (display_x < 0)
                display_x = 0;

            // 根据当前颜色模式选择绘制方式（竖向全字符显示）
            switch (colorState.currentColorMode)
            {
            case COLOR_MODE_SPECIFIC:
            case COLOR_MODE_RANDOM:
                drawString16x16VerticalWithCharColors(display_x, y, font_data, total_char_count, isUpper, 0);
                break;
            case COLOR_MODE_GRADIENT:
                drawString16x16VerticalGradient(display_x, y, font_data, total_char_count, isUpper, gradientMode);
                break;
            case COLOR_MODE_FIXED:
            default:
                drawString16x16Vertical(display_x, y, font_data, total_char_count, textColor);
                break;
            }
            return;
        }

        // 超过4个字符，按组显示
        int currentIndex = isUpper ? textState.upperIndex : textState.lowerIndex;
        int startCharIndex = currentIndex * maxCharsPerLine;
        int displayCharCount = min(maxCharsPerLine, total_char_count - startCharIndex);

        if (displayCharCount <= 0)
            return;

        // 计算居中位置
        int display_x = (SCREEN_WIDTH - displayCharCount * CHAR_SPACING_16) / 2;
        if (display_x < 0)
            display_x = 0;

        const uint16_t *current_font_data = font_data + (startCharIndex * 16);
        // 根据当前颜色模式选择绘制方式（竖向分组显示）
        switch (colorState.currentColorMode)
        {
        case COLOR_MODE_SPECIFIC:
        case COLOR_MODE_RANDOM:
            drawString16x16VerticalWithCharColors(display_x, y, current_font_data, displayCharCount, isUpper, startCharIndex);
            break;
        case COLOR_MODE_GRADIENT:
            drawString16x16VerticalGradient(display_x, y, current_font_data, displayCharCount, isUpper, gradientMode);
            break;
        case COLOR_MODE_FIXED:
        default:
            drawString16x16VerticalWithCharColors(display_x, y, current_font_data, displayCharCount, isUpper, startCharIndex);
            break;
        }
    }
    else
    {
        // 水平显示：每行最多显示4个字符
        const int maxCharsPerLine = SCREEN_WIDTH / CHAR_SPACING_16;

        // 如果字符数不超过4个，直接居中显示
        if (total_char_count <= maxCharsPerLine)
        {
            int x = (SCREEN_WIDTH - total_char_count * CHAR_SPACING_16) / 2;
            if (x < 0)
                x = 0;

            // 根据当前颜色模式选择绘制方式（水平全字符显示）
            switch (colorState.currentColorMode)
            {
            case COLOR_MODE_SPECIFIC:
            case COLOR_MODE_RANDOM:
                drawString16x16WithCharColors(x, y, font_data, total_char_count, isUpper, 0);
                break;
            case COLOR_MODE_GRADIENT:
                drawString16x16Gradient(x, y, font_data, total_char_count, isUpper, gradientMode);
                break;
            case COLOR_MODE_FIXED:
            default:
                drawString16x16WithCharColors(x, y, font_data, total_char_count, isUpper, 0);
                break;
            }
            return;
        }

        // 超过4个字符，按组显示
        int currentIndex = isUpper ? textState.upperIndex : textState.lowerIndex;
        int startCharIndex = currentIndex * maxCharsPerLine;
        int displayCharCount = min(maxCharsPerLine, total_char_count - startCharIndex);

        if (displayCharCount <= 0)
            return;

        // 计算居中位置
        int x = (SCREEN_WIDTH - displayCharCount * CHAR_SPACING_16) / 2;
        if (x < 0)
            x = 0;

        // 显示当前组的字符
        const uint16_t *current_font_data = font_data + (startCharIndex * 16);
        // 根据当前颜色模式选择绘制方式（水平分组显示）
        switch (colorState.currentColorMode)
        {
        case COLOR_MODE_SPECIFIC:
        case COLOR_MODE_RANDOM:
            drawString16x16WithCharColors(x, y, current_font_data, displayCharCount, isUpper, startCharIndex);
            break;
        case COLOR_MODE_GRADIENT:
            drawString16x16Gradient(x, y, current_font_data, displayCharCount, isUpper, gradientMode);
            break;
        case COLOR_MODE_FIXED:
        default:
            drawString16x16WithCharColors(x, y, current_font_data, displayCharCount, isUpper, startCharIndex);
            break;
        }
    }
}

// 32x32全屏文本显示函数（仿照16x16逻辑）
void displayFullScreenText32x32()
{
    // 检查闪烁特效是否应该隐藏文本（仿照16x16逻辑）
    bool blinkActive = effectState.upperBlinkActive; // 32x32全屏使用上半屏闪烁状态
    bool blinkVisible = effectState.upperBlinkVisible;

    if (blinkActive && !blinkVisible)
    {
        return; // 闪烁特效激活且当前应该隐藏，直接返回
    }

    // 使用全屏点阵数据（优先使用动态数据）
    const uint16_t *font_data;
    int total_char_count;

    if (dynamic_full_text && dynamic_full_char_count > 0)
    {
        // 优先使用动态数据
        font_data = dynamic_full_text;
        total_char_count = dynamic_full_char_count;
    }
    else
    {
        // 否则使用静态数据
        font_data = full_text;
        total_char_count = getFullTextCharCount();
    }

    // 检查是否启用滚动特效（仿照16x16逻辑）
    bool scrollActive = effectState.upperScrollActive; // 32x32全屏使用上半屏滚动状态

    // 检查是否使用渐变色 - 32x32全屏使用全局渐变设置
    bool useGradient = (colorState.currentColorMode == COLOR_MODE_GRADIENT);
    uint8_t gradientMode = colorState.gradientMode; // 使用全局渐变模式

    // 获取基础颜色（用于固定色和呼吸特效）
    uint16_t baseColor = colorState.textColor;
    uint16_t textColor = baseColor;

    // 应用呼吸特效到颜色（仅对固定色有效，渐变色不支持呼吸特效）
    bool breatheActive = effectState.upperBreatheActive; // 32x32全屏使用上半屏呼吸状态
    if (breatheActive && !useGradient)
    {
        float phase = effectState.upperBreathePhase;
        float brightness = (sin(phase) + 1.0) / 2.0; // 0.0 到 1.0 的正弦波
        brightness = 0.05 + brightness * 0.95;       // 范围从5%-100%，更强烈的对比度

        // 提取RGB分量（RGB565格式）
        uint8_t r = (baseColor >> 11) & 0x1F; // 5位红色
        uint8_t g = (baseColor >> 5) & 0x3F;  // 6位绿色
        uint8_t b = baseColor & 0x1F;         // 5位蓝色

        // 应用呼吸亮度
        r = (uint8_t)(r * brightness);
        g = (uint8_t)(g * brightness);
        b = (uint8_t)(b * brightness);

        // 重新组合颜色
        textColor = (r << 11) | (g << 5) | b;
    }

    // 检查显示方向
    bool isVertical = (textState.displayDirection == BT_DIRECTION_VERTICAL);

    if (scrollActive)
    {
        // 滚动模式：显示所有字符
        uint8_t scrollType = effectState.upperScrollType;
        int scrollOffset = effectState.upperScrollOffset;
        displayScrollingText32x32(font_data, total_char_count, scrollOffset, 0, scrollType);
        return;
    }

    // 32x32字体居中显示和分组切换功能
    // 32x32字体全屏最多显示2个字符（64像素宽度/32像素每字符）
    const int maxCharsPerScreen = SCREEN_WIDTH / CHAR_SPACING_32;

    if (isVertical)
    {
        // 竖向显示：每屏最多显示2个字符
        if (total_char_count <= maxCharsPerScreen)
        {
            // 字符数不超过最大显示数，直接居中显示
            int display_x = (SCREEN_WIDTH - total_char_count * CHAR_SPACING_32) / 2;
            if (display_x < 0)
                display_x = 0;

            // 根据当前颜色模式选择绘制方式（32x32竖向全字符显示）
            switch (colorState.currentColorMode)
            {
            case COLOR_MODE_SPECIFIC:
            case COLOR_MODE_RANDOM:
                drawString32x32VerticalWithCharColors(display_x, 0, font_data, total_char_count, 0);
                break;
            case COLOR_MODE_GRADIENT:
                drawString32x32VerticalGradient(display_x, 0, font_data, total_char_count, gradientMode);
                break;
            case COLOR_MODE_FIXED:
            default:
                drawString32x32Vertical(display_x, 0, font_data, total_char_count, textColor);
                break;
            }
        }
        else
        {
            // 超过最大显示数，按组显示
            int currentIndex = textState.upperIndex; // 使用上半屏的索引
            int startCharIndex = currentIndex * maxCharsPerScreen;
            int displayCharCount = min(maxCharsPerScreen, total_char_count - startCharIndex);

            if (displayCharCount > 0)
            {
                // 计算居中位置
                int display_x = (SCREEN_WIDTH - displayCharCount * CHAR_SPACING_32) / 2;
                if (display_x < 0)
                    display_x = 0;

                const uint16_t *current_font_data = font_data + (startCharIndex * 64);
                // 根据当前颜色模式选择绘制方式（32x32竖向分组显示）
                switch (colorState.currentColorMode)
                {
                case COLOR_MODE_SPECIFIC:
                case COLOR_MODE_RANDOM:
                    drawString32x32VerticalWithCharColors(display_x, 0, current_font_data, displayCharCount, startCharIndex);
                    break;
                case COLOR_MODE_GRADIENT:
                    drawString32x32VerticalGradient(display_x, 0, current_font_data, displayCharCount, gradientMode);
                    break;
                case COLOR_MODE_FIXED:
                default:
                    drawString32x32Vertical(display_x, 0, current_font_data, displayCharCount, textColor);
                    break;
                }
            }
        }
    }
    else
    {
        // 水平显示：每屏最多显示2个字符
        if (total_char_count <= maxCharsPerScreen)
        {
            // 字符数不超过最大显示数，直接居中显示
            int x = (SCREEN_WIDTH - total_char_count * CHAR_SPACING_32) / 2;
            if (x < 0)
                x = 0;

            // 根据当前颜色模式选择绘制方式（32x32水平全字符显示）
            switch (colorState.currentColorMode)
            {
            case COLOR_MODE_SPECIFIC:
            case COLOR_MODE_RANDOM:
                drawString32x32WithCharColors(x, 0, font_data, total_char_count, 0);
                break;
            case COLOR_MODE_GRADIENT:
                drawString32x32Gradient(x, 0, font_data, total_char_count, gradientMode);
                break;
            case COLOR_MODE_FIXED:
            default:
                drawString32x32(x, 0, font_data, total_char_count, textColor);
                break;
            }
        }
        else
        {
            // 超过最大显示数，按组显示
            int currentIndex = textState.upperIndex; // 使用上半屏的索引
            int startCharIndex = currentIndex * maxCharsPerScreen;
            int displayCharCount = min(maxCharsPerScreen, total_char_count - startCharIndex);

            if (displayCharCount > 0)
            {
                // 计算居中位置
                int x = (SCREEN_WIDTH - displayCharCount * CHAR_SPACING_32) / 2;
                if (x < 0)
                    x = 0;

                const uint16_t *current_font_data = font_data + (startCharIndex * 64);
                // 根据当前颜色模式选择绘制方式（32x32水平分组显示）
                switch (colorState.currentColorMode)
                {
                case COLOR_MODE_SPECIFIC:
                case COLOR_MODE_RANDOM:
                    drawString32x32WithCharColors(x, 0, current_font_data, displayCharCount, startCharIndex);
                    break;
                case COLOR_MODE_GRADIENT:
                    drawString32x32Gradient(x, 0, current_font_data, displayCharCount, gradientMode);
                    break;
                case COLOR_MODE_FIXED:
                default:
                    drawString32x32(x, 0, current_font_data, displayCharCount, textColor);
                    break;
                }
            }
        }
    }
}

// 处理点阵数据命令（新的函数签名）
void handleTextCommand(const uint16_t *upperData, int upperCharCount, const uint16_t *lowerData, int lowerCharCount)
{
    Serial.printf("设置点阵数据 - 上半屏: %d字符, 下半屏: %d字符\n", upperCharCount, lowerCharCount);

    // 🔄 重要：设置新文本时清除特定字符颜色，避免意外继承
    if (colorState.currentColorMode == COLOR_MODE_SPECIFIC)
    {
        Serial.println("🧹 检测到文本变更，自动清除特定字符颜色设置");
        switchColorMode(COLOR_MODE_FIXED);
    }

    // 释放旧的动态数据
    if (dynamic_upper_text)
    {
        free(dynamic_upper_text);
        dynamic_upper_text = nullptr;
    }
    if (dynamic_lower_text)
    {
        free(dynamic_lower_text);
        dynamic_lower_text = nullptr;
    }

    // 分配并复制上半屏数据
    if (upperData && upperCharCount > 0)
    {
        int upperDataSize = upperCharCount * 16 * sizeof(uint16_t); // 16x16字体每字符16个uint16_t
        dynamic_upper_text = (uint16_t *)malloc(upperDataSize);
        if (dynamic_upper_text)
        {
            memcpy(dynamic_upper_text, upperData, upperDataSize);
            dynamic_upper_char_count = upperCharCount;
            Serial.printf("上半屏数据已存储: %d字符, %d字节\n", upperCharCount, upperDataSize);
        }
        else
        {
            Serial.println("错误: 上半屏数据内存分配失败");
            dynamic_upper_char_count = 0;
        }
    }
    else
    {
        dynamic_upper_char_count = 0;
    }

    // 分配并复制下半屏数据
    if (lowerData && lowerCharCount > 0)
    {
        int lowerDataSize = lowerCharCount * 16 * sizeof(uint16_t); // 16x16字体每字符16个uint16_t
        dynamic_lower_text = (uint16_t *)malloc(lowerDataSize);
        if (dynamic_lower_text)
        {
            memcpy(dynamic_lower_text, lowerData, lowerDataSize);
            dynamic_lower_char_count = lowerCharCount;
            Serial.printf("下半屏数据已存储: %d字符, %d字节\n", lowerCharCount, lowerDataSize);
        }
        else
        {
            Serial.println("错误: 下半屏数据内存分配失败");
            dynamic_lower_char_count = 0;
        }
    }
    else
    {
        dynamic_lower_char_count = 0;
    }

    // 更新显示状态
    textState.upperIndex = 0;
    textState.lowerIndex = 0;
    textState.lastSwitchTime = millis();
    textState.needUpdate = true;
}

// 处理32x32全屏点阵数据命令
void handleFullScreenTextCommand(const uint16_t *fontData, int charCount)
{
    Serial.printf("设置32x32全屏点阵数据: %d字符\n", charCount);

    // 🔄 重要：设置新文本时清除特定字符颜色，避免意外继承
    if (colorState.currentColorMode == COLOR_MODE_SPECIFIC)
    {
        Serial.println("🧹 检测到32x32文本变更，自动清除特定字符颜色设置");
        switchColorMode(COLOR_MODE_FIXED);
    }

    // 释放旧的动态数据
    if (dynamic_full_text)
    {
        free(dynamic_full_text);
        dynamic_full_text = nullptr;
    }

    // 分配并复制全屏数据
    if (fontData && charCount > 0)
    {
        int dataSize = charCount * 64 * sizeof(uint16_t); // 32x32字体每字符64个uint16_t
        dynamic_full_text = (uint16_t *)malloc(dataSize);
        if (dynamic_full_text)
        {
            memcpy(dynamic_full_text, fontData, dataSize);
            dynamic_full_char_count = charCount;
            Serial.printf("全屏数据已存储: %d字符, %d字节\n", charCount, dataSize);
        }
        else
        {
            Serial.println("错误: 全屏数据内存分配失败");
            dynamic_full_char_count = 0;
        }
    }
    else
    {
        dynamic_full_char_count = 0;
    }

    // 更新显示状态
    textState.upperIndex = 0;
    textState.lastSwitchTime = millis();
    textState.needUpdate = true;
}

// 处理显示方向命令
void handleDirectionCommand(uint8_t direction)
{
    Serial.printf("设置显示方向: %s\n",
                  (direction == BT_DIRECTION_HORIZONTAL) ? "正向显示" : "竖向显示");

    // 更新显示方向状态
    textState.displayDirection = direction;
    textState.needUpdate = true; // 触发重绘
}

// 更新文本显示
void updateTextDisplay()
{
    unsigned long currentTime = millis();
    const unsigned long switchInterval = 2000; // 2秒切换间隔

    // 32x32字体使用简化逻辑
    if (currentFontSize == BT_FONT_32x32)
    {
        // 32x32字体分组切换逻辑 (仿照16x16逻辑)
        const int maxCharsPerScreen = SCREEN_WIDTH / CHAR_SPACING_32; // 每屏最多2个字符

        if (currentTime - textState.lastSwitchTime >= switchInterval)
        {
            // 获取字符总数（优先使用动态数据）
            int totalChars = (dynamic_full_text && dynamic_full_char_count > 0)
                                 ? dynamic_full_char_count
                                 : getFullTextCharCount();
            if (totalChars > maxCharsPerScreen)
            {
                int totalGroups = (totalChars + maxCharsPerScreen - 1) / maxCharsPerScreen;
                textState.upperIndex++;
                if (textState.upperIndex >= totalGroups)
                {
                    textState.upperIndex = 0;
                }
                textState.lastSwitchTime = currentTime;
                textState.needUpdate = true; // 关键：设置needUpdate标志！
            }
        }

        if (!textState.needUpdate && !colorState.needColorUpdate)
        {
            return;
        }

        // 清除屏幕并填充背景色
        dma_display->clearScreen();
        if (colorState.upperBackgroundColor != 0x0000)
        {
            for (int y = 0; y < SCREEN_HEIGHT; y++)
            {
                for (int x = 0; x < SCREEN_WIDTH; x++)
                {
                    dma_display->drawPixel(x, y, colorState.upperBackgroundColor);
                }
            }
        }

        // 显示32x32全屏文本
        displayFullScreenText32x32();

        // 绘制边框（最后绘制，确保在最上层）
        drawBorderFrame();

        textState.needUpdate = false;
        colorState.needColorUpdate = false; // 重置颜色更新标志
        return;
    }

    // 16x16字体的原有逻辑
    // 根据显示方向确定每组最大字符数（动态计算）
    bool isVertical = (textState.displayDirection == BT_DIRECTION_VERTICAL);
    const int maxCharsPerGroup = SCREEN_WIDTH / CHAR_SPACING_16; // 动态计算：64/16=4个字符每组

    // 检查是否需要切换显示内容
    if (currentTime - textState.lastSwitchTime >= switchInterval)
    {
        bool needUpdate = false;

        // 处理上半屏分组切换（优先使用动态数据）
        int upperTotalChars = (dynamic_upper_text && dynamic_upper_char_count > 0)
                                  ? dynamic_upper_char_count
                                  : getUpperTextCharCount();
        if (upperTotalChars > maxCharsPerGroup)
        {
            int upperTotalGroups = (upperTotalChars + maxCharsPerGroup - 1) / maxCharsPerGroup; // 向上取整
            textState.upperIndex++;
            if (textState.upperIndex >= upperTotalGroups)
            {
                textState.upperIndex = 0;
            }
            needUpdate = true;
        }

        // 处理下半屏分组切换（优先使用动态数据）
        int lowerTotalChars = (dynamic_lower_text && dynamic_lower_char_count > 0)
                                  ? dynamic_lower_char_count
                                  : getLowerTextCharCount();
        if (lowerTotalChars > maxCharsPerGroup)
        {
            int lowerTotalGroups = (lowerTotalChars + maxCharsPerGroup - 1) / maxCharsPerGroup; // 向上取整
            textState.lowerIndex++;
            if (textState.lowerIndex >= lowerTotalGroups)
            {
                textState.lowerIndex = 0;
            }
            needUpdate = true;
        }

        if (needUpdate)
        {
            textState.lastSwitchTime = currentTime;
            textState.needUpdate = true;
        }
    }

    if (!textState.needUpdate)
    {
        return;
    }

    // 清除屏幕
    dma_display->clearScreen();

    // 分别填充上下半屏背景色
    // 上半屏背景（Y坐标0-15）
    if (colorState.upperBackgroundColor != 0x0000)
    {
        for (int y = 0; y < 16; y++)
        {
            for (int x = 0; x < SCREEN_WIDTH; x++)
            {
                dma_display->drawPixel(x, y, colorState.upperBackgroundColor);
            }
        }
    }

    // 下半屏背景（Y坐标16-31）
    if (colorState.lowerBackgroundColor != 0x0000)
    {
        for (int y = 16; y < 32; y++)
        {
            for (int x = 0; x < SCREEN_WIDTH; x++)
            {
                dma_display->drawPixel(x, y, colorState.lowerBackgroundColor);
            }
        }
    }

    // 显示上半屏文本（Y坐标0）
    displayTextOnHalf(0, true);

    // 显示下半屏文本（Y坐标16）
    displayTextOnHalf(16, false);

    // 绘制边框（最后绘制，确保在最上层）
    drawBorderFrame();

    textState.needUpdate = false;
}

// ==================== 颜色相关函数 ====================
// 处理颜色命令
void handleColorCommand(const BluetoothFrame &frame)
{
    if (frame.dataLength < BT_COLOR_DATA_LEN)
    {
        Serial.printf("错误: 颜色数据长度不足，需要%d字节，收到%d字节\n", BT_COLOR_DATA_LEN, frame.dataLength);
        return;
    }

    // 使用getColorData方法解析颜色数据
    uint8_t screenArea, target, mode, r, g, b, gradientMode;
    frame.getColorData(screenArea, target, mode, r, g, b, gradientMode);

    // 验证参数范围
    if (screenArea < BT_SCREEN_UPPER || screenArea > BT_SCREEN_BOTH)
    {
        Serial.printf("错误: 无效的屏幕区域 0x%02X\n", screenArea);
        return;
    }

    if (target != BT_COLOR_TARGET_TEXT && target != BT_COLOR_TARGET_BACKGROUND)
    {
        Serial.printf("错误: 无效的目标类型 0x%02X\n", target);
        return;
    }

    // ⚠️ 重要限制：背景不可设置渐变色
    if (target == BT_COLOR_TARGET_BACKGROUND && mode == BT_COLOR_MODE_GRADIENT)
    {
        Serial.println("错误: 背景不支持渐变色，只有文本可以设置渐变色");
        return;
    }

    const char *areaName = (screenArea == BT_SCREEN_UPPER) ? "上半屏" : (screenArea == BT_SCREEN_LOWER) ? "下半屏"
                                                                    : (screenArea == BT_SCREEN_BOTH)    ? "全屏"
                                                                                                        : "未知区域";
    const char *targetName = (target == BT_COLOR_TARGET_TEXT) ? "文本" : "背景";
    const char *modeName = (mode == BT_COLOR_MODE_FIXED) ? "固定色" : "渐变色";

    Serial.printf("设置颜色 - 区域: %s, 目标: %s, 模式: %s, RGB: (%d,%d,%d), 渐变模式: 0x%02X\n",
                  areaName, targetName, modeName, r, g, b, gradientMode);

    // 检查是否为取消渐变色命令（渐变模式为0x00）
    if (gradientMode == 0x00 && target == BT_COLOR_TARGET_TEXT && mode == BT_COLOR_MODE_GRADIENT)
    {
        Serial.println("取消渐变色，恢复最近一次固定色设置");

        if (screenArea == BT_SCREEN_UPPER || screenArea == BT_SCREEN_BOTH)
        {
            colorState.upperTextMode = BT_COLOR_MODE_FIXED;
            colorState.upperGradientMode = BT_GRADIENT_FIXED;

            // 检查是否有记录的RGB值，如果有则恢复，否则使用白色
            if (colorState.upperTextR != 0 || colorState.upperTextG != 0 || colorState.upperTextB != 0)
            {
                colorState.upperTextColor = rgb888to565(colorState.upperTextR, colorState.upperTextG, colorState.upperTextB);
                Serial.printf("上半屏恢复到最近颜色: RGB(%d,%d,%d)\n",
                              colorState.upperTextR, colorState.upperTextG, colorState.upperTextB);
            }
            else
            {
                colorState.upperTextColor = COLOR_WHITE;
                colorState.upperTextR = 255;
                colorState.upperTextG = 255;
                colorState.upperTextB = 255;
                Serial.println("上半屏无历史颜色记录，恢复默认白色");
            }
        }

        if (screenArea == BT_SCREEN_LOWER || screenArea == BT_SCREEN_BOTH)
        {
            colorState.lowerTextMode = BT_COLOR_MODE_FIXED;
            colorState.lowerGradientMode = BT_GRADIENT_FIXED;

            // 检查是否有记录的RGB值，如果有则恢复，否则使用白色
            if (colorState.lowerTextR != 0 || colorState.lowerTextG != 0 || colorState.lowerTextB != 0)
            {
                colorState.lowerTextColor = rgb888to565(colorState.lowerTextR, colorState.lowerTextG, colorState.lowerTextB);
                Serial.printf("下半屏恢复到最近颜色: RGB(%d,%d,%d)\n",
                              colorState.lowerTextR, colorState.lowerTextG, colorState.lowerTextB);
            }
            else
            {
                colorState.lowerTextColor = COLOR_WHITE;
                colorState.lowerTextR = 255;
                colorState.lowerTextG = 255;
                colorState.lowerTextB = 255;
                Serial.println("下半屏无历史颜色记录，恢复默认白色");
            }
        }

        if (screenArea == BT_SCREEN_BOTH)
        {
            colorState.textMode = BT_COLOR_MODE_FIXED;
            colorState.gradientMode = BT_GRADIENT_FIXED;

            // 检查是否有记录的RGB值，如果有则恢复，否则使用白色
            if (colorState.textR != 0 || colorState.textG != 0 || colorState.textB != 0)
            {
                colorState.textColor = rgb888to565(colorState.textR, colorState.textG, colorState.textB);
                Serial.printf("全屏恢复到最近颜色: RGB(%d,%d,%d)\n",
                              colorState.textR, colorState.textG, colorState.textB);
            }
            else
            {
                colorState.textColor = COLOR_WHITE;
                colorState.textR = 255;
                colorState.textG = 255;
                colorState.textB = 255;
                Serial.println("全屏无历史颜色记录，恢复默认白色");
            }
        }

        colorState.needColorUpdate = true;
        textState.needUpdate = true;
        return; // 直接返回，不执行后续的颜色设置逻辑
    }

    // 根据屏幕区域设置颜色
    if (screenArea == BT_SCREEN_UPPER)
    {
        // 上半屏颜色设置
        if (target == BT_COLOR_TARGET_TEXT)
        {
            colorState.upperTextR = r;
            colorState.upperTextG = g;
            colorState.upperTextB = b;
            colorState.upperTextMode = mode;
            colorState.upperGradientMode = gradientMode;
            if (mode == BT_COLOR_MODE_FIXED)
            {
                colorState.upperTextColor = rgb888to565(r, g, b);
            }
        }
        else if (target == BT_COLOR_TARGET_BACKGROUND)
        {
            colorState.upperBgR = r;
            colorState.upperBgG = g;
            colorState.upperBgB = b;
            colorState.upperBgMode = mode;
            if (mode == BT_COLOR_MODE_FIXED)
            {
                colorState.upperBackgroundColor = rgb888to565(r, g, b);
            }
        }
    }
    else if (screenArea == BT_SCREEN_LOWER)
    {
        // 下半屏颜色设置
        if (target == BT_COLOR_TARGET_TEXT)
        {
            colorState.lowerTextR = r;
            colorState.lowerTextG = g;
            colorState.lowerTextB = b;
            colorState.lowerTextMode = mode;
            colorState.lowerGradientMode = gradientMode;
            if (mode == BT_COLOR_MODE_FIXED)
            {
                colorState.lowerTextColor = rgb888to565(r, g, b);
            }
        }
        else if (target == BT_COLOR_TARGET_BACKGROUND)
        {
            colorState.lowerBgR = r;
            colorState.lowerBgG = g;
            colorState.lowerBgB = b;
            colorState.lowerBgMode = mode;
            if (mode == BT_COLOR_MODE_FIXED)
            {
                colorState.lowerBackgroundColor = rgb888to565(r, g, b);
            }
        }
    }
    else if (screenArea == BT_SCREEN_BOTH)
    {
        // 全屏颜色设置（同时设置上下半屏）
        if (target == BT_COLOR_TARGET_TEXT)
        {
            // 设置全屏文本颜色
            colorState.textR = r;
            colorState.textG = g;
            colorState.textB = b;
            colorState.textMode = mode;
            colorState.gradientMode = gradientMode;
            // 同时设置上下半屏
            colorState.upperTextR = r;
            colorState.upperTextG = g;
            colorState.upperTextB = b;
            colorState.upperTextMode = mode;
            colorState.upperGradientMode = gradientMode;
            colorState.lowerTextR = r;
            colorState.lowerTextG = g;
            colorState.lowerTextB = b;
            colorState.lowerTextMode = mode;
            colorState.lowerGradientMode = gradientMode;

            if (mode == BT_COLOR_MODE_FIXED)
            {
                uint16_t color = rgb888to565(r, g, b);
                colorState.textColor = color;
                colorState.upperTextColor = color;
                colorState.lowerTextColor = color;
            }
        }
        else if (target == BT_COLOR_TARGET_BACKGROUND)
        {
            // 设置全屏背景颜色
            colorState.bgR = r;
            colorState.bgG = g;
            colorState.bgB = b;
            colorState.bgMode = mode;
            // 同时设置上下半屏
            colorState.upperBgR = r;
            colorState.upperBgG = g;
            colorState.upperBgB = b;
            colorState.upperBgMode = mode;
            colorState.lowerBgR = r;
            colorState.lowerBgG = g;
            colorState.lowerBgB = b;
            colorState.lowerBgMode = mode;

            if (mode == BT_COLOR_MODE_FIXED)
            {
                uint16_t color = rgb888to565(r, g, b);
                colorState.backgroundColor = color;
                colorState.upperBackgroundColor = color;
                colorState.lowerBackgroundColor = color;
            }
        }
    }

    // 根据模式切换颜色模式
    if (target == BT_COLOR_TARGET_TEXT)
    {
        if (mode == BT_COLOR_MODE_FIXED)
        {
            switchColorMode(COLOR_MODE_FIXED);
        }
        else if (mode == BT_COLOR_MODE_GRADIENT)
        {
            switchColorMode(COLOR_MODE_GRADIENT);
        }
    }

    colorState.needColorUpdate = true;
    textState.needUpdate = true; // 触发文本重绘
}

// 更新颜色状态
void updateColors()
{
    if (!colorState.needColorUpdate)
    {
        return;
    }
}

// ==================== 亮度相关函数 ====================
// 处理亮度命令
void handleBrightnessCommand(const BluetoothFrame &frame)
{
    uint8_t brightness = frame.getBrightnessData();

    Serial.printf("设置亮度: %d (%.1f%%)\n", brightness, brightness * 100.0 / 255.0);

    // 更新亮度状态
    brightnessState.brightness = brightness;
    brightnessState.needBrightnessUpdate = true;

    // 立即应用亮度设置
    updateBrightness();
}

// 更新亮度设置
void updateBrightness()
{
    if (brightnessState.needBrightnessUpdate)
    {
        // 设置LED矩阵亮度
        dma_display->setBrightness8(brightnessState.brightness);

        Serial.printf("亮度已更新: %d (%.1f%%)\n",
                      brightnessState.brightness,
                      brightnessState.brightness * 100.0 / 255.0);

        brightnessState.needBrightnessUpdate = false;
        textState.needUpdate = true; // 触发重绘以应用新亮度
    }
}

// 处理特效命令
void handleEffectCommand(const BluetoothFrame &frame)
{
    if (frame.dataLength < BT_EFFECT_DATA_LEN)
    {
        Serial.printf("错误: 特效数据长度不足，需要%d字节，收到%d字节\n", BT_EFFECT_DATA_LEN, frame.dataLength);
        return;
    }

    uint8_t screenArea, effectType, speed;
    frame.getEffectData(screenArea, effectType, speed);

    // 验证参数范围
    if (speed < 1 || speed > 10)
    {
        Serial.printf("警告: 特效速度超出建议范围(1-10)，当前值: %d\n", speed);
    }

    const char *areaName = (screenArea == BT_SCREEN_UPPER) ? "上半屏" : (screenArea == BT_SCREEN_LOWER) ? "下半屏"
                                                                    : (screenArea == BT_SCREEN_BOTH)    ? "全屏"
                                                                                                        : "未知区域";

    const char *effectName = (effectType == BT_EFFECT_FIXED) ? "固定显示" : (effectType == BT_EFFECT_SCROLL_LEFT) ? "左移滚动"
                                                                        : (effectType == BT_EFFECT_SCROLL_RIGHT)  ? "右移滚动"
                                                                        : (effectType == BT_EFFECT_BLINK)         ? "闪烁特效"
                                                                        : (effectType == BT_EFFECT_BREATHE)       ? "呼吸特效"
                                                                        : (effectType == BT_EFFECT_SCROLL_UP)     ? "向上滚动"
                                                                        : (effectType == BT_EFFECT_SCROLL_DOWN)   ? "向下滚动"
                                                                                                                  : "未知特效";

    Serial.printf("处理特效命令 - 区域: %s, 特效: %s, 速度: %d\n", areaName, effectName, speed);

    bool isUpper = (screenArea == BT_SCREEN_UPPER || screenArea == BT_SCREEN_BOTH);
    bool isLower = (screenArea == BT_SCREEN_LOWER || screenArea == BT_SCREEN_BOTH);

    // 应用特效到指定区域
    if (isUpper)
    {
        clearAllEffects(true);
        switch (effectType)
        {
        case BT_EFFECT_SCROLL_LEFT:
        case BT_EFFECT_SCROLL_RIGHT:
        case BT_EFFECT_SCROLL_UP:
        case BT_EFFECT_SCROLL_DOWN:
            setScrollEffect(true, effectType, speed);
            break;
        case BT_EFFECT_BLINK:
            setBlinkEffect(true, speed);
            break;
        case BT_EFFECT_BREATHE:
            setBreatheEffect(true, speed);
            break;
        case BT_EFFECT_FIXED:
        default:
            Serial.println("上半屏特效已清除（固定显示）");
            break;
        }
    }

    if (isLower)
    {
        clearAllEffects(false);
        switch (effectType)
        {
        case BT_EFFECT_SCROLL_LEFT:
        case BT_EFFECT_SCROLL_RIGHT:
        case BT_EFFECT_SCROLL_UP:
        case BT_EFFECT_SCROLL_DOWN:
            setScrollEffect(false, effectType, speed);
            break;
        case BT_EFFECT_BLINK:
            setBlinkEffect(false, speed);
            break;
        case BT_EFFECT_BREATHE:
            setBreatheEffect(false, speed);
            break;
        case BT_EFFECT_FIXED:
        default:
            Serial.println("下半屏特效已清除（固定显示）");
            break;
        }
    }
}

// ==================== 特效相关函数 ====================
// 清除指定半屏的所有特效（实现互斥限制）
void clearAllEffects(bool isUpper)
{
    if (isUpper)
    {
        // 清除上半屏所有特效
        effectState.upperScrollActive = false;
        effectState.upperBlinkActive = false;
        effectState.upperBreatheActive = false;
        Serial.println("已清除上半屏所有特效");
    }
    else
    {
        // 清除下半屏所有特效
        effectState.lowerScrollActive = false;
        effectState.lowerBlinkActive = false;
        effectState.lowerBreatheActive = false;
        Serial.println("已清除下半屏所有特效");
    }
    textState.needUpdate = true;
}

// 设置滚动特效（自动清除其他特效）
void setScrollEffect(bool isUpper, uint8_t scrollType, uint8_t speed)
{
    // 先清除该半屏的所有特效
    clearAllEffects(isUpper);

    // 设置滚动特效
    if (isUpper)
    {
        effectState.upperScrollActive = true;
        effectState.upperScrollType = scrollType;
        effectState.upperScrollSpeed = speed;
        effectState.upperScrollOffset = 0;
        const char *scrollName = (scrollType == BT_EFFECT_SCROLL_LEFT) ? "左滚动" : (scrollType == BT_EFFECT_SCROLL_RIGHT) ? "右滚动"
                                                                                : (scrollType == BT_EFFECT_SCROLL_UP)      ? "向上滚动"
                                                                                : (scrollType == BT_EFFECT_SCROLL_DOWN)    ? "向下滚动"
                                                                                                                           : "未知滚动";
        Serial.printf("上半屏启用滚动特效 - 类型: %s, 速度: %d\n", scrollName, speed);
    }
    else
    {
        effectState.lowerScrollActive = true;
        effectState.lowerScrollType = scrollType;
        effectState.lowerScrollSpeed = speed;
        effectState.lowerScrollOffset = 0;
        const char *scrollName = (scrollType == BT_EFFECT_SCROLL_LEFT) ? "左滚动" : (scrollType == BT_EFFECT_SCROLL_RIGHT) ? "右滚动"
                                                                                : (scrollType == BT_EFFECT_SCROLL_UP)      ? "向上滚动"
                                                                                : (scrollType == BT_EFFECT_SCROLL_DOWN)    ? "向下滚动"
                                                                                                                           : "未知滚动";
        Serial.printf("下半屏启用滚动特效 - 类型: %s, 速度: %d\n", scrollName, speed);
    }
    textState.needUpdate = true;
}

// 设置闪烁特效（自动清除其他特效）
void setBlinkEffect(bool isUpper, uint8_t speed)
{
    // 先清除该半屏的所有特效
    clearAllEffects(isUpper);

    // 设置闪烁特效
    if (isUpper)
    {
        effectState.upperBlinkActive = true;
        effectState.upperBlinkSpeed = speed;
        effectState.upperBlinkVisible = true;
        Serial.printf("上半屏启用闪烁特效 - 速度: %d\n", speed);
    }
    else
    {
        effectState.lowerBlinkActive = true;
        effectState.lowerBlinkSpeed = speed;
        effectState.lowerBlinkVisible = true;
        Serial.printf("下半屏启用闪烁特效 - 速度: %d\n", speed);
    }
    textState.needUpdate = true;
}

// 设置呼吸特效（自动清除其他特效）
void setBreatheEffect(bool isUpper, uint8_t speed)
{
    // 先清除该半屏的所有特效
    clearAllEffects(isUpper);

    // 设置呼吸特效
    if (isUpper)
    {
        effectState.upperBreatheActive = true;
        effectState.upperBreatheSpeed = speed;
        effectState.upperBreathePhase = 0.0;
        Serial.printf("上半屏启用呼吸特效 - 速度: %d\n", speed);
    }
    else
    {
        effectState.lowerBreatheActive = true;
        effectState.lowerBreatheSpeed = speed;
        effectState.lowerBreathePhase = 0.0;
        Serial.printf("下半屏启用呼吸特效 - 速度: %d\n", speed);
    }
    textState.needUpdate = true;
}

// 显示滚动文本（支持水平和竖向显示，支持呼吸特效）
void displayScrollingText(const uint16_t *font_data, int char_count, int offset, int y, uint8_t scrollType)
{
    if (char_count == 0)
        return;

    bool isUpper = (y < 16);
    bool isVertical = (textState.displayDirection == BT_DIRECTION_VERTICAL);
    int textPixelWidth = char_count * CHAR_SPACING_16; // 文本总像素宽度
    int x = 0;

    // 根据滚动类型和显示方向计算位置
    if (scrollType == BT_EFFECT_SCROLL_LEFT || scrollType == BT_EFFECT_SCROLL_UP)
    {
        // 左滚动或向上滚动：从右边进入，向左移动
        x = SCREEN_WIDTH - offset;
    }
    else if (scrollType == BT_EFFECT_SCROLL_RIGHT || scrollType == BT_EFFECT_SCROLL_DOWN)
    {
        // 右滚动或向下滚动：从左边进入，向右移动
        x = offset - textPixelWidth;
    }

    // 只在屏幕范围内绘制文本
    if (x < SCREEN_WIDTH && x + textPixelWidth > 0)
    {
        // 检查是否使用渐变色
        uint8_t textMode = isUpper ? colorState.upperTextMode : colorState.lowerTextMode;
        uint8_t gradientMode = isUpper ? colorState.upperGradientMode : colorState.lowerGradientMode;
        bool useGradient = (textMode == BT_COLOR_MODE_GRADIENT && gradientMode != BT_GRADIENT_FIXED);

        if (useGradient)
        {
            // 使用渐变色显示（渐变色不支持呼吸特效）
            if (isVertical)
            {
                drawString16x16VerticalGradient(x, y, font_data, char_count, isUpper, gradientMode);
            }
            else
            {
                drawString16x16Gradient(x, y, font_data, char_count, isUpper, gradientMode);
            }
        }
        else
        {
            // 获取基础颜色
            uint16_t baseColor = isUpper ? colorState.upperTextColor : colorState.lowerTextColor;
            uint16_t textColor = baseColor;

            // 应用呼吸特效（如果激活）
            bool breatheActive = isUpper ? effectState.upperBreatheActive : effectState.lowerBreatheActive;
            if (breatheActive)
            {
                float phase = isUpper ? effectState.upperBreathePhase : effectState.lowerBreathePhase;
                float brightness = (sin(phase) + 1.0) / 2.0; // 0.0 到 1.0 的正弦波
                brightness = 0.05 + brightness * 0.95;       // 范围从5%-100%，更强烈的呼吸对比度

                // 提取RGB分量（RGB565格式）
                uint8_t r = (baseColor >> 11) & 0x1F; // 5位红色
                uint8_t g = (baseColor >> 5) & 0x3F;  // 6位绿色
                uint8_t b = baseColor & 0x1F;         // 5位蓝色

                // 应用呼吸亮度
                r = (uint8_t)(r * brightness);
                g = (uint8_t)(g * brightness);
                b = (uint8_t)(b * brightness);

                // 重新组合颜色
                textColor = (r << 11) | (g << 5) | b;
            }

            // 根据显示方向使用对应的绘制函数
            if (isVertical)
            {
                drawString16x16Vertical(x, y, font_data, char_count, textColor);
            }
            else
            {
                drawString16x16(x, y, font_data, char_count, textColor);
            }
        }
    }
}

// 显示32x32滚动文本（仿照16x16逻辑）
void displayScrollingText32x32(const uint16_t *font_data, int char_count, int offset, int y, uint8_t scrollType)
{
    if (char_count == 0)
        return;

    bool isVertical = (textState.displayDirection == BT_DIRECTION_VERTICAL);
    int textPixelWidth = char_count * CHAR_SPACING_32; // 32x32文本总像素宽度
    int x = 0;

    // 根据滚动类型和显示方向计算位置
    if (scrollType == BT_EFFECT_SCROLL_LEFT || scrollType == BT_EFFECT_SCROLL_UP)
    {
        // 左滚动或向上滚动：从右边进入，向左移动
        x = SCREEN_WIDTH - offset;
    }
    else if (scrollType == BT_EFFECT_SCROLL_RIGHT || scrollType == BT_EFFECT_SCROLL_DOWN)
    {
        // 右滚动或向下滚动：从左边进入，向右移动
        x = offset - textPixelWidth;
    }

    // 只在屏幕范围内绘制文本
    if (x < SCREEN_WIDTH && x + textPixelWidth > 0)
    {
        // 检查是否使用渐变色 - 32x32全屏使用全局渐变设置
        bool useGradient = (colorState.currentColorMode == COLOR_MODE_GRADIENT);
        uint8_t gradientMode = colorState.gradientMode; // 使用全局渐变模式

        if (useGradient)
        {
            // 使用渐变色显示（渐变色不支持呼吸特效）
            if (isVertical)
            {
                drawString32x32VerticalGradient(x, y, font_data, char_count, gradientMode);
            }
            else
            {
                drawString32x32Gradient(x, y, font_data, char_count, gradientMode);
            }
        }
        else
        {
            // 获取基础颜色
            uint16_t baseColor = colorState.textColor;
            uint16_t textColor = baseColor;

            // 应用呼吸特效（如果激活）- 32x32使用上半屏呼吸状态
            bool breatheActive = effectState.upperBreatheActive;
            if (breatheActive)
            {
                float phase = effectState.upperBreathePhase;
                float brightness = (sin(phase) + 1.0) / 2.0; // 0.0 到 1.0 的正弦波
                brightness = 0.05 + brightness * 0.95;       // 范围从5%-100%，更强烈的呼吸对比度

                // 提取RGB分量（RGB565格式）
                uint8_t r = (baseColor >> 11) & 0x1F; // 5位红色
                uint8_t g = (baseColor >> 5) & 0x3F;  // 6位绿色
                uint8_t b = baseColor & 0x1F;         // 5位蓝色

                // 应用呼吸亮度
                r = (uint8_t)(r * brightness);
                g = (uint8_t)(g * brightness);
                b = (uint8_t)(b * brightness);

                // 重新组合颜色
                textColor = (r << 11) | (g << 5) | b;
            }

            // 根据显示方向使用对应的绘制函数
            if (isVertical)
            {
                drawString32x32Vertical(x, y, font_data, char_count, textColor);
            }
            else
            {
                drawString32x32(x, y, font_data, char_count, textColor);
            }
        }
    }
}

// 更新滚动特效（支持速度控制）
void updateScrollEffect()
{
    if (!effectState.upperScrollActive && !effectState.lowerScrollActive)
    {
        return;
    }

    unsigned long currentTime = millis();
    static unsigned long lastUpperScrollTime = 0;
    static unsigned long lastLowerScrollTime = 0;
    bool needUpdate = false;

    // 更新上半屏滚动
    if (effectState.upperScrollActive)
    {
        // 根据速度计算滚动间隔：速度越高间隔越短，移动距离越大
        unsigned long upperScrollInterval = 100 - (effectState.upperScrollSpeed * 8); // 速度0-10对应100ms-20ms
        int upperMoveDistance = (effectState.upperScrollSpeed / 2) + 1;               // 速度0-10对应1-6像素

        if (currentTime - lastUpperScrollTime >= upperScrollInterval)
        {
            int upperCharCount, textPixelWidth;

            // 根据字体大小计算字符数和像素宽度（优先使用动态数据）
            if (currentFontSize == BT_FONT_32x32)
            {
                // 32x32字体：优先使用动态数据
                upperCharCount = (dynamic_full_text && dynamic_full_char_count > 0)
                                     ? dynamic_full_char_count
                                     : getFullTextCharCount();
                textPixelWidth = upperCharCount * CHAR_SPACING_32;
            }
            else
            {
                // 16x16字体：优先使用动态数据
                upperCharCount = (dynamic_upper_text && dynamic_upper_char_count > 0)
                                     ? dynamic_upper_char_count
                                     : getUpperTextCharCount();
                textPixelWidth = upperCharCount * CHAR_SPACING_16;
            }

            int maxOffset = SCREEN_WIDTH + textPixelWidth; // 完全滚出屏幕的偏移量

            effectState.upperScrollOffset += upperMoveDistance;
            if (effectState.upperScrollOffset >= maxOffset)
            {
                effectState.upperScrollOffset = 0; // 重新开始滚动
            }
            lastUpperScrollTime = currentTime;
            needUpdate = true;
        }
    }

    // 更新下半屏滚动
    if (effectState.lowerScrollActive)
    {
        // 根据速度计算滚动间隔：速度越高间隔越短，移动距离越大
        unsigned long lowerScrollInterval = 100 - (effectState.lowerScrollSpeed * 8); // 速度0-10对应100ms-20ms
        int lowerMoveDistance = (effectState.lowerScrollSpeed / 2) + 1;               // 速度0-10对应1-6像素

        if (currentTime - lastLowerScrollTime >= lowerScrollInterval)
        {
            // 下半屏：优先使用动态数据
            int lowerCharCount = (dynamic_lower_text && dynamic_lower_char_count > 0)
                                     ? dynamic_lower_char_count
                                     : getLowerTextCharCount();
            int textPixelWidth = lowerCharCount * CHAR_SPACING_16;
            int maxOffset = SCREEN_WIDTH + textPixelWidth; // 完全滚出屏幕的偏移量

            effectState.lowerScrollOffset += lowerMoveDistance;
            if (effectState.lowerScrollOffset >= maxOffset)
            {
                effectState.lowerScrollOffset = 0; // 重新开始滚动
            }
            lastLowerScrollTime = currentTime;
            needUpdate = true;
        }
    }

    if (needUpdate)
    {
        textState.needUpdate = true;
    }
}

// 更新闪烁特效（支持速度控制）
void updateBlinkEffect()
{
    if (!effectState.upperBlinkActive && !effectState.lowerBlinkActive)
    {
        return;
    }

    unsigned long currentTime = millis();
    static unsigned long lastUpperBlinkTime = 0;
    static unsigned long lastLowerBlinkTime = 0;
    bool needUpdate = false;

    // 上半屏闪烁
    if (effectState.upperBlinkActive)
    {
        // 根据速度计算闪烁间隔：速度越高间隔越短
        unsigned long upperBlinkInterval = 1000 - (effectState.upperBlinkSpeed * 80); // 速度0-10对应1000ms-200ms

        if (currentTime - lastUpperBlinkTime >= upperBlinkInterval)
        {
            effectState.upperBlinkVisible = !effectState.upperBlinkVisible;
            lastUpperBlinkTime = currentTime;
            needUpdate = true;
        }
    }

    // 下半屏闪烁
    if (effectState.lowerBlinkActive)
    {
        // 根据速度计算闪烁间隔：速度越高间隔越短
        unsigned long lowerBlinkInterval = 1000 - (effectState.lowerBlinkSpeed * 80); // 速度0-10对应1000ms-200ms

        if (currentTime - lastLowerBlinkTime >= lowerBlinkInterval)
        {
            effectState.lowerBlinkVisible = !effectState.lowerBlinkVisible;
            lastLowerBlinkTime = currentTime;
            needUpdate = true;
        }
    }

    if (needUpdate)
    {
        textState.needUpdate = true;
    }
}

// 更新呼吸特效（支持速度控制）
void updateBreatheEffect()
{
    if (!effectState.upperBreatheActive && !effectState.lowerBreatheActive)
    {
        return;
    }

    unsigned long currentTime = millis();
    static unsigned long lastBreatheTime = 0;
    const unsigned long breatheInterval = 30; // 30ms更新间隔，保证平滑

    if (currentTime - lastBreatheTime >= breatheInterval)
    {
        bool needUpdate = false;

        // 上半屏呼吸
        if (effectState.upperBreatheActive)
        {
            // 根据速度计算相位增量：速度越高变化越快（增加呼吸速度）
            float upperSpeed = (effectState.upperBreatheSpeed + 1) * 0.15; // 速度0-10对应0.15-1.65的相位增量
            effectState.upperBreathePhase += upperSpeed;
            if (effectState.upperBreathePhase >= 2 * PI)
            {
                effectState.upperBreathePhase -= 2 * PI;
            }
            needUpdate = true;
        }

        // 下半屏呼吸
        if (effectState.lowerBreatheActive)
        {
            // 根据速度计算相位增量：速度越高变化越快（增加呼吸速度）
            float lowerSpeed = (effectState.lowerBreatheSpeed + 1) * 0.15; // 速度0-10对应0.15-1.65的相位增量
            effectState.lowerBreathePhase += lowerSpeed;
            if (effectState.lowerBreathePhase >= 2 * PI)
            {
                effectState.lowerBreathePhase -= 2 * PI;
            }
            needUpdate = true;
        }

        if (needUpdate)
        {
            lastBreatheTime = currentTime;
            textState.needUpdate = true;
        }
    }
}

// 更新所有特效
void updateAllEffects()
{
    // 分别更新各种特效，每种特效有自己的时间控制
    updateScrollEffect();
    updateBlinkEffect();
    updateBreatheEffect();
    updateBorderEffect(); // 添加边框特效更新
}

// ==================== 示例演示函数 ====================
// 演示如何使用蓝牙点阵数据
void demoBluetoothDataUsage()
{
    Serial.println("=== 蓝牙点阵数据使用示例 ===");

    // 示例1：16x16模式 - 传入自定义点阵数据
    if (currentFontSize == BT_FONT_16x16)
    {
        // 假设这是从蓝牙接收到的点阵数据
        uint16_t customUpperData[] = {
            // 自定义上半屏点阵数据 (1个字符，16个uint16_t)
            0x0000, 0x0180, 0x03C0, 0x07E0, 0x0FF0, 0x1FF8, 0x3FFC, 0x7FFE,
            0x7FFE, 0x3FFC, 0x1FF8, 0x0FF0, 0x07E0, 0x03C0, 0x0180, 0x0000};

        uint16_t customLowerData[] = {
            // 自定义下半屏点阵数据 (1个字符，16个uint16_t)
            0xFFFF, 0x8001, 0x8001, 0x8001, 0x8001, 0x8001, 0x8001, 0x8001,
            0x8001, 0x8001, 0x8001, 0x8001, 0x8001, 0x8001, 0x8001, 0xFFFF};

        // 调用新的API传入点阵数据
        handleTextCommand(customUpperData, 1, customLowerData, 1);
        Serial.println("16x16自定义点阵数据已设置");
    }

    // 示例2：32x32模式 - 传入自定义点阵数据
    else if (currentFontSize == BT_FONT_32x32)
    {
        // 假设这是从蓝牙接收到的32x32点阵数据
        uint16_t custom32x32Data[64] = {0}; // 1个字符，64个uint16_t，这里简化为全0
        // 实际使用时这里应该是真正的32x32点阵数据
        for (int i = 10; i < 54; i++)
        {
            custom32x32Data[i] = 0xFFFF; // 创建一个简单的图案
        }

        // 调用新的API传入点阵数据
        handleFullScreenTextCommand(custom32x32Data, 1);
        Serial.println("32x32自定义点阵数据已设置");
    }

    Serial.println("=== 使用说明 ===");
    Serial.println("1. 接收蓝牙数据：uint16_t* bluetoothData, int charCount");
    Serial.println("2. 16x16模式调用：handleTextCommand(upperData, upperCount, lowerData, lowerCount)");
    Serial.println("3. 32x32模式调用：handleFullScreenTextCommand(fontData, charCount)");
    Serial.println("4. 数据会自动显示，无需其他操作");
}

// ==================== 边框相关函数 ====================

// 设置边框效果
void setBorderEffect(uint8_t style, uint16_t color, uint8_t effect, uint8_t speed)
{
    borderState.active = (style != 0); // style=0表示无边框
    borderState.style = style;
    borderState.fixedColor = color; // 对于彩虹边框，此参数将被忽略
    borderState.effect = effect;
    borderState.speed = speed;

    // 重置运行时状态
    borderState.flowOffset = 0;
    borderState.blinkVisible = true;
    borderState.lastUpdateTime = millis();
    borderState.needUpdate = true;

    // 调试输出
    Serial.printf("🔧 setBorderEffect - style:%d, effect:%d, speed:%d\n", style, effect, speed);

    // 触发显示更新
    textState.needUpdate = true;

    Serial.printf("边框设置 - 样式:%d, 颜色:0x%04X, 效果:%d, 速度:%d, 激活:%s\n",
                  style, color, effect, speed, borderState.active ? "是" : "否");
}

// 清除边框效果
void clearBorderEffect()
{
    borderState.active = false;
    borderState.needUpdate = true;
    textState.needUpdate = true;
    Serial.println("边框已清除");
}

// 获取内容区域（被边框束缚的区域）
void getContentArea(int &contentX, int &contentY, int &contentWidth, int &contentHeight)
{
    if (!borderState.active)
    {
        // 无边框时，内容区域就是整个屏幕
        contentX = 0;
        contentY = 0;
        contentWidth = SCREEN_WIDTH;
        contentHeight = SCREEN_HEIGHT;
        return;
    }

    // 有边框时，内容区域需要减去边框占用的像素
    switch (borderState.style)
    {
    case 1: // 实线边框：四周各占1像素
        contentX = 1;
        contentY = 1;
        contentWidth = SCREEN_WIDTH - 2;
        contentHeight = SCREEN_HEIGHT - 2;
        break;

    case 2: // 点线边框：四周各占1像素
        contentX = 1;
        contentY = 1;
        contentWidth = SCREEN_WIDTH - 2;
        contentHeight = SCREEN_HEIGHT - 2;
        break;

    case 3: // 角落边框：角落不影响内容区域大小，但视觉上有束缚感
        contentX = 0;
        contentY = 0;
        contentWidth = SCREEN_WIDTH;
        contentHeight = SCREEN_HEIGHT;
        break;

    default:
        contentX = 0;
        contentY = 0;
        contentWidth = SCREEN_WIDTH;
        contentHeight = SCREEN_HEIGHT;
        break;
    }
}

// 获取边框颜色（简化版，仅返回固定色）
uint16_t getBorderColor(int x, int y)
{
    return borderState.fixedColor;
}

// 绘制实线边框（固定色）
void drawSolidBorder()
{
    uint16_t color = borderState.fixedColor;

    // 上边框
    for (int x = 0; x < SCREEN_WIDTH; x++)
    {
        dma_display->drawPixel(x, 0, color);
    }

    // 下边框
    for (int x = 0; x < SCREEN_WIDTH; x++)
    {
        dma_display->drawPixel(x, SCREEN_HEIGHT - 1, color);
    }

    // 左边框
    for (int y = 0; y < SCREEN_HEIGHT; y++)
    {
        dma_display->drawPixel(0, y, color);
    }

    // 右边框
    for (int y = 0; y < SCREEN_HEIGHT; y++)
    {
        dma_display->drawPixel(SCREEN_WIDTH - 1, y, color);
    }
}

// 绘制彩虹边框（基于之前的流动彩虹实现）
void drawRainbowBorder()
{
    if (borderState.effect == 1 || borderState.effect == 2)
    {
        // 流动效果：渐变色沿着边框环形路径流动
        drawRainbowBorderWithFlow();
    }
    else
    {
        // 静态彩虹边框：固定位置的彩虹色
        drawRainbowBorderStatic();
    }
}

// 绘制静态彩虹边框
void drawRainbowBorderStatic()
{
    // 上边框
    for (int x = 0; x < SCREEN_WIDTH; x++)
    {
        uint16_t color = getRainbowColor(x, 0);
        dma_display->drawPixel(x, 0, color);
    }

    // 下边框
    for (int x = 0; x < SCREEN_WIDTH; x++)
    {
        uint16_t color = getRainbowColor(x, SCREEN_HEIGHT - 1);
        dma_display->drawPixel(x, SCREEN_HEIGHT - 1, color);
    }

    // 左边框
    for (int y = 0; y < SCREEN_HEIGHT; y++)
    {
        uint16_t color = getRainbowColor(0, y);
        dma_display->drawPixel(0, y, color);
    }

    // 右边框
    for (int y = 0; y < SCREEN_HEIGHT; y++)
    {
        uint16_t color = getRainbowColor(SCREEN_WIDTH - 1, y);
        dma_display->drawPixel(SCREEN_WIDTH - 1, y, color);
    }
}

// 绘制流动效果的彩虹边框
void drawRainbowBorderWithFlow()
{
    // 计算边框的总周长（像素数）
    int totalPerimeter = (SCREEN_WIDTH - 1) * 2 + (SCREEN_HEIGHT - 1) * 2;

    // 流动偏移量（直接使用updateBorderEffect中计算的偏移）
    int offset = borderState.flowOffset;

    // 定义渐变色数量（可调整）
    const int gradientLength = SCREEN_WIDTH / 4; // 渐变色跨越16个像素

    // 遍历边框的每个像素，按照环形路径计算位置
    for (int i = 0; i < totalPerimeter; i++)
    {
        int x, y;

        // 将线性位置转换为边框上的(x,y)坐标
        // 路径：上边→右边→下边→左边
        if (i < SCREEN_WIDTH - 1)
        {
            // 上边框：从左到右
            x = i;
            y = 0;
        }
        else if (i < SCREEN_WIDTH - 1 + SCREEN_HEIGHT - 1)
        {
            // 右边框：从上到下
            x = SCREEN_WIDTH - 1;
            y = i - (SCREEN_WIDTH - 1) + 1;
        }
        else if (i < (SCREEN_WIDTH - 1) * 2 + SCREEN_HEIGHT - 1)
        {
            // 下边框：从右到左
            x = SCREEN_WIDTH - 1 - (i - (SCREEN_WIDTH - 1 + SCREEN_HEIGHT - 1));
            y = SCREEN_HEIGHT - 1;
        }
        else
        {
            // 左边框：从下到上
            x = 0;
            y = SCREEN_HEIGHT - 1 - (i - ((SCREEN_WIDTH - 1) * 2 + SCREEN_HEIGHT - 1));
        }

        // 计算当前像素在流动渐变中的位置
        int flowPosition = (i + offset) % totalPerimeter;
        if (flowPosition < 0)
            flowPosition += totalPerimeter;

        // 计算渐变色索引（0-6，对应7种渐变色）
        int colorIndex = (flowPosition * 7) / gradientLength % 7;

        // 使用预定义的7种颜色（彩虹色）
        uint8_t rainbowColors[7][3] = {
            {255, 0, 0},   // 红
            {255, 127, 0}, // 橙
            {255, 255, 0}, // 黄
            {0, 255, 0},   // 绿
            {0, 255, 255}, // 青
            {0, 0, 255},   // 蓝
            {127, 0, 255}  // 紫
        };

        uint8_t r = rainbowColors[colorIndex][0];
        uint8_t g = rainbowColors[colorIndex][1];
        uint8_t b = rainbowColors[colorIndex][2];
        uint16_t color = rgb888to565(r, g, b);

        dma_display->drawPixel(x, y, color);
    }
}

// 获取彩虹颜色（用于静态彩虹边框）
uint16_t getRainbowColor(int x, int y)
{
    // 根据位置计算彩虹色索引
    int totalPos = x + y;
    int colorIndex = totalPos % 7;

    uint8_t rainbowColors[7][3] = {
        {255, 0, 0},   // 红
        {255, 127, 0}, // 橙
        {255, 255, 0}, // 黄
        {0, 255, 0},   // 绿
        {0, 255, 255}, // 青
        {0, 0, 255},   // 蓝
        {127, 0, 255}  // 紫
    };

    uint8_t r = rainbowColors[colorIndex][0];
    uint8_t g = rainbowColors[colorIndex][1];
    uint8_t b = rainbowColors[colorIndex][2];
    return rgb888to565(r, g, b);
}

// 绘制点线边框
void drawDottedBorder()
{
    if (borderState.effect == 1 || borderState.effect == 2)
    {
        // 流动效果：使用环形路径
        drawDottedBorderWithFlow();
    }
    else
    {
        // 静态效果：固定位置的点
        drawDottedBorderStatic();
    }
}

// 绘制静态点线边框
void drawDottedBorderStatic()
{
    uint16_t color = borderState.fixedColor;
    int dotSpacing = 3;

    // 上边框
    for (int x = 0; x < SCREEN_WIDTH; x += dotSpacing)
    {
        dma_display->drawPixel(x, 0, color);
    }

    // 下边框
    for (int x = 0; x < SCREEN_WIDTH; x += dotSpacing)
    {
        dma_display->drawPixel(x, SCREEN_HEIGHT - 1, color);
    }

    // 左边框
    for (int y = 0; y < SCREEN_HEIGHT; y += dotSpacing)
    {
        dma_display->drawPixel(0, y, color);
    }

    // 右边框
    for (int y = 0; y < SCREEN_HEIGHT; y += dotSpacing)
    {
        dma_display->drawPixel(SCREEN_WIDTH - 1, y, color);
    }
}

// 绘制流动效果的点线边框（环形路径）
void drawDottedBorderWithFlow()
{
    uint16_t color = borderState.fixedColor;
    int dotSpacing = 3;

    // 计算边框的总周长（像素数）
    int totalPerimeter = (SCREEN_WIDTH - 1) * 2 + (SCREEN_HEIGHT - 1) * 2;

    // 流动偏移量（直接使用updateBorderEffect中计算的偏移）
    int offset = borderState.flowOffset;

    // 遍历边框的每个像素，按照环形路径计算位置
    for (int i = 0; i < totalPerimeter; i++)
    {
        // 计算流动位置
        int flowPosition = (i + offset) % totalPerimeter;
        if (flowPosition < 0)
            flowPosition += totalPerimeter;

        // 只在符合点线间隔的位置绘制
        if (flowPosition % dotSpacing != 0)
            continue;

        int x, y;

        // 将线性位置转换为边框上的(x,y)坐标
        // 路径：上边→右边→下边→左边
        if (i < SCREEN_WIDTH - 1)
        {
            // 上边框：从左到右
            x = i;
            y = 0;
        }
        else if (i < SCREEN_WIDTH - 1 + SCREEN_HEIGHT - 1)
        {
            // 右边框：从上到下
            x = SCREEN_WIDTH - 1;
            y = i - (SCREEN_WIDTH - 1) + 1;
        }
        else if (i < (SCREEN_WIDTH - 1) * 2 + SCREEN_HEIGHT - 1)
        {
            // 下边框：从右到左
            x = SCREEN_WIDTH - 1 - (i - (SCREEN_WIDTH - 1 + SCREEN_HEIGHT - 1));
            y = SCREEN_HEIGHT - 1;
        }
        else
        {
            // 左边框：从下到上
            x = 0;
            y = SCREEN_HEIGHT - 1 - (i - ((SCREEN_WIDTH - 1) * 2 + SCREEN_HEIGHT - 1));
        }

        dma_display->drawPixel(x, y, color);
    }
}

// 绘制角落边框
void drawCornerBorder()
{
    uint16_t color = borderState.fixedColor; // 角落边框只使用固定色
    int cornerSize = 3;                      // 角落大小

    // 左上角
    for (int i = 0; i < cornerSize; i++)
    {
        dma_display->drawPixel(i, 0, color); // 水平线
        dma_display->drawPixel(0, i, color); // 垂直线
    }

    // 右上角
    for (int i = 0; i < cornerSize; i++)
    {
        dma_display->drawPixel(SCREEN_WIDTH - 1 - i, 0, color); // 水平线
        dma_display->drawPixel(SCREEN_WIDTH - 1, i, color);     // 垂直线
    }

    // 左下角
    for (int i = 0; i < cornerSize; i++)
    {
        dma_display->drawPixel(i, SCREEN_HEIGHT - 1, color);     // 水平线
        dma_display->drawPixel(0, SCREEN_HEIGHT - 1 - i, color); // 垂直线
    }

    // 右下角
    for (int i = 0; i < cornerSize; i++)
    {
        dma_display->drawPixel(SCREEN_WIDTH - 1 - i, SCREEN_HEIGHT - 1, color); // 水平线
        dma_display->drawPixel(SCREEN_WIDTH - 1, SCREEN_HEIGHT - 1 - i, color); // 垂直线
    }
}

// 绘制边框
void drawBorderFrame()
{
    if (!borderState.active || borderState.style == 0)
    {
        return; // 无边框或样式为0时不绘制
    }

    // 检查闪烁效果
    if (borderState.effect == 3 && !borderState.blinkVisible)
    {
        return; // 闪烁效果且当前不可见
    }

    // 调试信息（限制输出频率，避免刷屏）
    static unsigned long lastDebugTime = 0;
    if (millis() - lastDebugTime > 1000)
    { // 每秒最多输出一次
        Serial.printf("绘制边框 - 样式:%d, 激活:%s, 闪烁可见:%s, 颜色:0x%04X\n",
                      borderState.style, borderState.active ? "是" : "否",
                      borderState.blinkVisible ? "是" : "否", borderState.fixedColor);
        lastDebugTime = millis();
    }

    switch (borderState.style)
    {
    case BORDER_STYLE_SOLID: // 实线边框
        drawSolidBorder();
        break;

    case BORDER_STYLE_DOTTED: // 点线边框
        drawDottedBorder();
        break;

    case BORDER_STYLE_CORNER: // 角落边框
        drawCornerBorder();
        break;

    case BORDER_STYLE_RAINBOW: // 彩虹边框
        drawRainbowBorder();
        break;

    default:
        break;
    }
}

// 更新边框特效
void updateBorderEffect()
{
    if (!borderState.active || borderState.effect == 0)
    {
        return; // 无边框或无效果时不更新
    }

    unsigned long currentTime = millis();
    bool needUpdate = false;

    // 根据效果类型更新
    switch (borderState.effect)
    {
    case 1: // 顺时针流动
    case 2: // 逆时针流动
    {
        // 根据速度计算更新间隔
        unsigned long interval = 200 - (borderState.speed * 15); // 速度1-10对应185ms-50ms

        if (currentTime - borderState.lastUpdateTime >= interval)
        {
            int oldOffset = borderState.flowOffset;
            if (borderState.effect == 1)
            {
                borderState.flowOffset--; // 顺时针
                if (borderState.flowOffset < 0)
                {
                    borderState.flowOffset = 10; // 重置为正值
                }
            }
            else
            {
                borderState.flowOffset++; // 逆时针
            }
            borderState.lastUpdateTime = currentTime;
            needUpdate = true;

            // 调试输出（每10次更新输出一次，避免刷屏）
            static int debugCounter = 0;
            if (++debugCounter % 10 == 0)
            {
                Serial.printf("🔄 流动更新 - effect:%d, offset:%d→%d\n",
                              borderState.effect, oldOffset, borderState.flowOffset);
            }
        }
        break;
    }

    case 3: // 闪烁
    {
        // 根据速度计算闪烁间隔
        unsigned long interval = 1000 - (borderState.speed * 80); // 速度1-10对应920ms-200ms

        if (currentTime - borderState.lastUpdateTime >= interval)
        {
            borderState.blinkVisible = !borderState.blinkVisible;
            borderState.lastUpdateTime = currentTime;
            needUpdate = true;
        }
        break;
    }

    default:
        break;
    }

    if (needUpdate)
    {
        borderState.needUpdate = true;
        textState.needUpdate = true;
    }
}

// ==================== 新增颜色系统实现 ====================

// 初始化特定字符颜色系统
void initSpecificColorSystem()
{
    colorState.specificColor.enabled = false;
    colorState.specificColor.upperCharColors = nullptr;
    colorState.specificColor.upperHasCustomColor = nullptr;
    colorState.specificColor.maxUpperChars = 0;
    colorState.specificColor.lowerCharColors = nullptr;
    colorState.specificColor.lowerHasCustomColor = nullptr;
    colorState.specificColor.maxLowerChars = 0;
    Serial.println("特定字符颜色系统已初始化");
}

// 清除所有特定字符颜色
void clearSpecificColors()
{
    // 清除上半屏数据
    if (colorState.specificColor.upperCharColors)
    {
        free(colorState.specificColor.upperCharColors);
        colorState.specificColor.upperCharColors = nullptr;
    }
    if (colorState.specificColor.upperHasCustomColor)
    {
        free(colorState.specificColor.upperHasCustomColor);
        colorState.specificColor.upperHasCustomColor = nullptr;
    }

    // 清除下半屏数据
    if (colorState.specificColor.lowerCharColors)
    {
        free(colorState.specificColor.lowerCharColors);
        colorState.specificColor.lowerCharColors = nullptr;
    }
    if (colorState.specificColor.lowerHasCustomColor)
    {
        free(colorState.specificColor.lowerHasCustomColor);
        colorState.specificColor.lowerHasCustomColor = nullptr;
    }

    colorState.specificColor.enabled = false;
    colorState.specificColor.maxUpperChars = 0;
    colorState.specificColor.maxLowerChars = 0;

    Serial.println("特定字符颜色已清除");
    textState.needUpdate = true;
}

// 清除上半屏特定字符颜色
void clearUpperSpecificColors()
{
    if (colorState.specificColor.upperCharColors)
    {
        free(colorState.specificColor.upperCharColors);
        colorState.specificColor.upperCharColors = nullptr;
    }
    if (colorState.specificColor.upperHasCustomColor)
    {
        free(colorState.specificColor.upperHasCustomColor);
        colorState.specificColor.upperHasCustomColor = nullptr;
    }

    colorState.specificColor.maxUpperChars = 0;

    // 检查是否还有下半屏特定颜色，如果没有则完全关闭特定颜色系统
    if (colorState.specificColor.maxLowerChars == 0)
    {
        colorState.specificColor.enabled = false;
        switchColorMode(COLOR_MODE_FIXED);
        Serial.println("上半屏特定字符颜色已清除，系统切换到固定色模式");
    }
    else
    {
        Serial.println("上半屏特定字符颜色已清除，保留下半屏设置");
    }

    textState.needUpdate = true;
}

// 清除下半屏特定字符颜色
void clearLowerSpecificColors()
{
    if (colorState.specificColor.lowerCharColors)
    {
        free(colorState.specificColor.lowerCharColors);
        colorState.specificColor.lowerCharColors = nullptr;
    }
    if (colorState.specificColor.lowerHasCustomColor)
    {
        free(colorState.specificColor.lowerHasCustomColor);
        colorState.specificColor.lowerHasCustomColor = nullptr;
    }

    colorState.specificColor.maxLowerChars = 0;

    // 检查是否还有上半屏特定颜色，如果没有则完全关闭特定颜色系统
    if (colorState.specificColor.maxUpperChars == 0)
    {
        colorState.specificColor.enabled = false;
        switchColorMode(COLOR_MODE_FIXED);
        Serial.println("下半屏特定字符颜色已清除，系统切换到固定色模式");
    }
    else
    {
        Serial.println("下半屏特定字符颜色已清除，保留上半屏设置");
    }

    textState.needUpdate = true;
}

// 设置特定字符颜色（支持分屏独立索引）
void setSpecificCharColor(uint8_t screenArea, int charIndex, uint16_t color)
{
    // 如果当前是渐变色模式，则拒绝设置特定字符颜色（互斥规则）
    if (colorState.currentColorMode == COLOR_MODE_GRADIENT)
    {
        Serial.println("错误: 渐变色模式下不允许设置特定字符颜色（互斥限制）");
        return;
    }

    // 根据屏幕区域分别处理
    if (screenArea == BT_SCREEN_UPPER)
    {
        // 上半屏字符颜色设置
        int upperCharCount = (dynamic_upper_text && dynamic_upper_char_count > 0)
                                 ? dynamic_upper_char_count
                                 : getUpperTextCharCount();

        if (charIndex < 0 || charIndex >= upperCharCount)
        {
            Serial.printf("错误: 上半屏字符索引 %d 超出范围 (0-%d)\n", charIndex, upperCharCount - 1);
            return;
        }

        // 初始化或扩展上半屏数组
        if (colorState.specificColor.maxUpperChars < upperCharCount)
        {
            uint16_t *newColors = (uint16_t *)realloc(colorState.specificColor.upperCharColors,
                                                      upperCharCount * sizeof(uint16_t));
            bool *newFlags = (bool *)realloc(colorState.specificColor.upperHasCustomColor,
                                             upperCharCount * sizeof(bool));

            if (!newColors || !newFlags)
            {
                Serial.println("错误: 上半屏字符颜色内存分配失败");
                return;
            }

            // 初始化新增的部分
            for (int i = colorState.specificColor.maxUpperChars; i < upperCharCount; i++)
            {
                newColors[i] = COLOR_WHITE;
                newFlags[i] = false;
            }

            colorState.specificColor.upperCharColors = newColors;
            colorState.specificColor.upperHasCustomColor = newFlags;
            colorState.specificColor.maxUpperChars = upperCharCount;
        }

        // 设置上半屏字符颜色
        colorState.specificColor.upperCharColors[charIndex] = color;
        colorState.specificColor.upperHasCustomColor[charIndex] = true;
        Serial.printf("设置上半屏字符 %d 颜色: 0x%04X\n", charIndex, color);
    }
    else if (screenArea == BT_SCREEN_LOWER)
    {
        // 下半屏字符颜色设置
        int lowerCharCount = (dynamic_lower_text && dynamic_lower_char_count > 0)
                                 ? dynamic_lower_char_count
                                 : getLowerTextCharCount();

        if (charIndex < 0 || charIndex >= lowerCharCount)
        {
            Serial.printf("错误: 下半屏字符索引 %d 超出范围 (0-%d)\n", charIndex, lowerCharCount - 1);
            return;
        }

        // 初始化或扩展下半屏数组
        if (colorState.specificColor.maxLowerChars < lowerCharCount)
        {
            uint16_t *newColors = (uint16_t *)realloc(colorState.specificColor.lowerCharColors,
                                                      lowerCharCount * sizeof(uint16_t));
            bool *newFlags = (bool *)realloc(colorState.specificColor.lowerHasCustomColor,
                                             lowerCharCount * sizeof(bool));

            if (!newColors || !newFlags)
            {
                Serial.println("错误: 下半屏字符颜色内存分配失败");
                return;
            }

            // 初始化新增的部分
            for (int i = colorState.specificColor.maxLowerChars; i < lowerCharCount; i++)
            {
                newColors[i] = COLOR_WHITE;
                newFlags[i] = false;
            }

            colorState.specificColor.lowerCharColors = newColors;
            colorState.specificColor.lowerHasCustomColor = newFlags;
            colorState.specificColor.maxLowerChars = lowerCharCount;
        }

        // 设置下半屏字符颜色
        colorState.specificColor.lowerCharColors[charIndex] = color;
        colorState.specificColor.lowerHasCustomColor[charIndex] = true;
        Serial.printf("设置下半屏字符 %d 颜色: 0x%04X\n", charIndex, color);
    }
    else if (screenArea == BT_SCREEN_BOTH)
    {
        // 全屏设置（32x32字体） - 使用上半屏标志处理
        int fullCharCount = (dynamic_full_text && dynamic_full_char_count > 0)
                                ? dynamic_full_char_count
                                : getFullTextCharCount();

        if (charIndex < 0 || charIndex >= fullCharCount)
        {
            Serial.printf("错误: 全屏字符索引 %d 超出范围 (0-%d)\n", charIndex, fullCharCount - 1);
            return;
        }

        // 初始化或扩展上半屏数组（32x32全屏使用上半屏数据结构）
        if (colorState.specificColor.maxUpperChars < fullCharCount)
        {
            // 重新分配数组
            uint16_t *newColors = (uint16_t *)realloc(colorState.specificColor.upperCharColors,
                                                      fullCharCount * sizeof(uint16_t));
            bool *newFlags = (bool *)realloc(colorState.specificColor.upperHasCustomColor,
                                             fullCharCount * sizeof(bool));

            if (!newColors || !newFlags)
            {
                Serial.println("错误: 全屏字符颜色数组内存分配失败");
                return;
            }

            // 初始化新增的元素
            for (int i = colorState.specificColor.maxUpperChars; i < fullCharCount; i++)
            {
                newColors[i] = COLOR_WHITE;
                newFlags[i] = false;
            }

            colorState.specificColor.upperCharColors = newColors;
            colorState.specificColor.upperHasCustomColor = newFlags;
            colorState.specificColor.maxUpperChars = fullCharCount;
        }

        // 设置字符颜色
        colorState.specificColor.upperCharColors[charIndex] = color;
        colorState.specificColor.upperHasCustomColor[charIndex] = true;

        Serial.printf("✅ 全屏字符 %d 颜色已设置为 0x%04X\n", charIndex, color);
    }
    else
    {
        Serial.println("错误: 不支持的屏幕区域");
        return;
    }

    // 启用特定字符颜色系统并切换到特定字符颜色模式
    colorState.specificColor.enabled = true;
    colorState.currentColorMode = COLOR_MODE_SPECIFIC;
    textState.needUpdate = true;
}

// 获取特定字符颜色（支持分屏独立索引）
uint16_t getSpecificCharColor(int charIndex, bool isUpper)
{
    if (!colorState.specificColor.enabled)
    {
        return COLOR_WHITE;
    }

    if (isUpper)
    {
        // 上半屏字符颜色获取
        if (charIndex < 0 ||
            charIndex >= colorState.specificColor.maxUpperChars ||
            !colorState.specificColor.upperHasCustomColor ||
            !colorState.specificColor.upperHasCustomColor[charIndex])
        {
            return COLOR_WHITE;
        }
        return colorState.specificColor.upperCharColors[charIndex];
    }
    else
    {
        // 下半屏字符颜色获取
        if (charIndex < 0 ||
            charIndex >= colorState.specificColor.maxLowerChars ||
            !colorState.specificColor.lowerHasCustomColor ||
            !colorState.specificColor.lowerHasCustomColor[charIndex])
        {
            return COLOR_WHITE;
        }
        return colorState.specificColor.lowerCharColors[charIndex];
    }
}

// 检查字符是否有特定颜色（支持分屏独立索引）
bool hasSpecificCharColor(int charIndex, bool isUpper)
{
    if (!colorState.specificColor.enabled)
    {
        return false;
    }

    if (isUpper)
    {
        // 检查上半屏字符
        return (charIndex >= 0 &&
                charIndex < colorState.specificColor.maxUpperChars &&
                colorState.specificColor.upperHasCustomColor &&
                colorState.specificColor.upperHasCustomColor[charIndex]);
    }
    else
    {
        // 检查下半屏字符
        return (charIndex >= 0 &&
                charIndex < colorState.specificColor.maxLowerChars &&
                colorState.specificColor.lowerHasCustomColor &&
                colorState.specificColor.lowerHasCustomColor[charIndex]);
    }
}

// 初始化随机颜色系统
void initRandomColorSystem()
{
    colorState.randomColor.enabled = false;
    colorState.randomColor.mode = RANDOM_COLOR_OFF;
    colorState.randomColor.updateInterval = 0;
    colorState.randomColor.seed = 0;
    colorState.randomColor.lastUpdateTime = 0;
    colorState.randomColor.randomColors = nullptr;
    colorState.randomColor.colorCount = 0;
    colorState.randomColor.screenArea = 0x00;
    Serial.println("随机颜色系统已初始化");
}

// 清除随机颜色设置
void clearRandomColors()
{
    if (colorState.randomColor.randomColors)
    {
        free(colorState.randomColor.randomColors);
        colorState.randomColor.randomColors = nullptr;
    }

    colorState.randomColor.enabled = false;
    colorState.randomColor.mode = RANDOM_COLOR_OFF;
    colorState.randomColor.colorCount = 0;
    colorState.randomColor.screenArea = 0x00;

    Serial.println("随机颜色已清除");
    textState.needUpdate = true;
}

// 设置随机颜色模式（支持上下屏统一随机色）
void setRandomColorMode(uint8_t screenArea, uint8_t mode, uint8_t interval, uint8_t seed)
{
    if (mode == RANDOM_COLOR_OFF)
    {
        clearRandomColors();
        return;
    }

    // 获取当前字符总数
    int totalChars = 0;
    if (screenArea == BT_SCREEN_UPPER || screenArea == BT_SCREEN_BOTH)
    {
        totalChars = max(totalChars, (dynamic_upper_text && dynamic_upper_char_count > 0)
                                         ? dynamic_upper_char_count
                                         : getUpperTextCharCount());
    }
    if (screenArea == BT_SCREEN_LOWER || screenArea == BT_SCREEN_BOTH)
    {
        totalChars = max(totalChars, (dynamic_lower_text && dynamic_lower_char_count > 0)
                                         ? dynamic_lower_char_count
                                         : getLowerTextCharCount());
    }
    if (screenArea == BT_SCREEN_BOTH && currentFontSize == BT_FONT_32x32)
    {
        totalChars = (dynamic_full_text && dynamic_full_char_count > 0)
                         ? dynamic_full_char_count
                         : getFullTextCharCount();
    }

    if (totalChars == 0)
    {
        Serial.println("错误: 没有文本可设置随机颜色");
        return;
    }

    // 分配或重新分配内存
    if (colorState.randomColor.colorCount != totalChars)
    {
        uint16_t *newColors = (uint16_t *)realloc(colorState.randomColor.randomColors,
                                                  totalChars * sizeof(uint16_t));
        if (!newColors)
        {
            Serial.println("错误: 随机颜色内存分配失败");
            return;
        }
        colorState.randomColor.randomColors = newColors;
        colorState.randomColor.colorCount = totalChars;
    }

    // 设置参数
    colorState.randomColor.enabled = true;
    colorState.randomColor.mode = mode;
    colorState.randomColor.updateInterval = interval;
    colorState.randomColor.seed = seed;
    colorState.randomColor.screenArea = screenArea;
    colorState.randomColor.lastUpdateTime = millis();

    // 初始化随机数种子
    if (seed != 0)
    {
        srand(seed);
    }
    else
    {
        srand(millis()); // 使用当前时间作为种子
    }

    // 生成初始随机颜色
    if (mode == RANDOM_COLOR_ALL_SAME)
    {
        // 所有字符相同颜色
        uint16_t sameColor = generateRandomColorByMode(mode, seed);
        for (int i = 0; i < totalChars; i++)
        {
            colorState.randomColor.randomColors[i] = sameColor;
        }
    }
    else
    {
        // 每个字符不同颜色（使用不同的种子）
        for (int i = 0; i < totalChars; i++)
        {
            colorState.randomColor.randomColors[i] = generateRandomColorByMode(mode, seed + i);
        }
    }

    // 切换到随机颜色模式
    colorState.currentColorMode = COLOR_MODE_RANDOM;

    Serial.printf("设置随机颜色 - 模式:%d, 间隔:%d秒, 种子:%d, 字符数:%d\n",
                  mode, interval, seed, totalChars);
    textState.needUpdate = true;
}

// 更新随机颜色
void updateRandomColors()
{
    if (!colorState.randomColor.enabled ||
        colorState.randomColor.updateInterval == 0)
    {
        return;
    }

    unsigned long currentTime = millis();
    unsigned long intervalMs = colorState.randomColor.updateInterval * 1000;

    if (currentTime - colorState.randomColor.lastUpdateTime >= intervalMs)
    {
        // 重新生成随机颜色
        if (colorState.randomColor.mode == RANDOM_COLOR_ALL_SAME)
        {
            // 所有字符相同颜色
            uint16_t sameColor = generateRandomColorByMode(colorState.randomColor.mode,
                                                           colorState.randomColor.seed);
            for (int i = 0; i < colorState.randomColor.colorCount; i++)
            {
                colorState.randomColor.randomColors[i] = sameColor;
            }
        }
        else
        {
            // 每个字符不同颜色（使用不同的种子）
            for (int i = 0; i < colorState.randomColor.colorCount; i++)
            {
                colorState.randomColor.randomColors[i] = generateRandomColorByMode(
                    colorState.randomColor.mode, colorState.randomColor.seed + i);
            }
        }

        colorState.randomColor.lastUpdateTime = currentTime;
        textState.needUpdate = true;

        Serial.printf("随机颜色已更新 - 模式:%d\n", colorState.randomColor.mode);
    }
}

// 获取随机颜色
uint16_t getRandomColor(int charIndex, bool isUpper)
{
    if (!colorState.randomColor.enabled ||
        charIndex < 0 ||
        charIndex >= colorState.randomColor.colorCount)
    {
        return COLOR_WHITE; // 默认颜色
    }

    // 检查屏幕区域匹配
    uint8_t area = colorState.randomColor.screenArea;
    if (area == BT_SCREEN_BOTH ||
        (area == BT_SCREEN_UPPER && isUpper) ||
        (area == BT_SCREEN_LOWER && !isUpper))
    {
        return colorState.randomColor.randomColors[charIndex];
    }

    return COLOR_WHITE; // 区域不匹配时返回默认颜色
}

// 根据模式生成随机颜色
uint16_t generateRandomColorByMode(uint8_t mode, uint8_t seed)
{
    uint8_t r, g, b;

    // 使用种子初始化随机数生成器，确保相同种子产生相同颜色
    randomSeed(seed);

    switch (mode)
    {
    case RANDOM_COLOR_EACH_CHAR:
    case RANDOM_COLOR_ALL_SAME:
        // 完全随机色
        r = random(256);
        g = random(256);
        b = random(256);
        break;

    case RANDOM_COLOR_RAINBOW:
    {
        // 彩虹色随机选择
        const uint8_t rainbowColors[7][3] = {
            {255, 0, 0}, {255, 127, 0}, {255, 255, 0}, {0, 255, 0}, {0, 255, 255}, {0, 0, 255}, {127, 0, 255}};
        int index = random(7);
        r = rainbowColors[index][0];
        g = rainbowColors[index][1];
        b = rainbowColors[index][2];
        break;
    }

    case RANDOM_COLOR_WARM:
        // 暖色系：红橙黄为主
        r = random(128, 256);
        g = random(64, 256);
        b = random(0, 128);
        break;

    case RANDOM_COLOR_COOL:
        // 冷色系：蓝绿紫为主
        r = random(0, 128);
        g = random(64, 256);
        b = random(128, 256);
        break;

    case RANDOM_COLOR_BRIGHT:
        // 高亮度色彩
        r = random(2) ? random(200, 256) : random(0, 56);
        g = random(2) ? random(200, 256) : random(0, 56);
        b = random(2) ? random(200, 256) : random(0, 56);
        break;

    default:
        r = g = b = 255; // 默认白色
        break;
    }

    return rgb888to565(r, g, b);
}

// 切换颜色模式（实现时间顺序覆盖逻辑）
void switchColorMode(uint8_t newMode)
{
    // 清理旧模式的数据（根据需要）
    switch (colorState.currentColorMode)
    {
    case COLOR_MODE_SPECIFIC:
        if (newMode != COLOR_MODE_SPECIFIC)
        {
            Serial.println("切换模式：清除特定字符颜色");
            clearSpecificColors();
        }
        break;

    case COLOR_MODE_RANDOM:
        if (newMode != COLOR_MODE_RANDOM)
        {
            Serial.println("切换模式：清除随机颜色");
            clearRandomColors();
        }
        break;

    case COLOR_MODE_GRADIENT:
        if (newMode != COLOR_MODE_GRADIENT)
        {
            Serial.println("切换模式：清除渐变色设置");
            // 清除渐变色模式标志
            colorState.upperTextMode = BT_COLOR_MODE_FIXED;
            colorState.lowerTextMode = BT_COLOR_MODE_FIXED;
            colorState.upperGradientMode = BT_GRADIENT_FIXED;
            colorState.lowerGradientMode = BT_GRADIENT_FIXED;
        }
        break;

    case COLOR_MODE_FIXED:
        // 固定色不需要额外清理
        break;
    }

    colorState.currentColorMode = newMode;
    Serial.printf("颜色模式已切换: %d\n", newMode);
    textState.needUpdate = true;
}

// 获取字符最终颜色（统一入口，高瞻远瞩的设计，支持呼吸特效）
uint16_t getCharacterColor(int charIndex, bool isUpper, int x, int y)
{
    uint16_t baseColor;

    // 按照时间顺序覆盖的逻辑，最后设置的模式优先
    switch (colorState.currentColorMode)
    {
    case COLOR_MODE_SPECIFIC:
        // 特定字符颜色模式
        if (hasSpecificCharColor(charIndex, isUpper))
        {
            baseColor = getSpecificCharColor(charIndex, isUpper);
        }
        else
        {
            // 没有特定颜色的字符使用默认固定色
            baseColor = isUpper ? colorState.upperTextColor : colorState.lowerTextColor;
        }
        break;

    case COLOR_MODE_RANDOM:
        // 随机颜色模式（如果区域不匹配则使用固定色）
        {
            uint16_t randomColor = getRandomColor(charIndex, isUpper);
            if (randomColor != COLOR_WHITE ||
                (colorState.randomColor.screenArea == BT_SCREEN_BOTH ||
                 (colorState.randomColor.screenArea == BT_SCREEN_UPPER && isUpper) ||
                 (colorState.randomColor.screenArea == BT_SCREEN_LOWER && !isUpper)))
            {
                baseColor = randomColor;
            }
            else
            {
                // 区域不匹配时使用固定色
                baseColor = isUpper ? colorState.upperTextColor : colorState.lowerTextColor;
            }
        }
        break;

    case COLOR_MODE_GRADIENT:
        // 渐变色模式（渐变色不支持呼吸特效，直接返回）
        {
            uint8_t gradientMode = isUpper ? colorState.upperGradientMode : colorState.lowerGradientMode;
            return getGradientColor(x, y, isUpper, gradientMode);
        }

    case COLOR_MODE_FIXED:
    default:
        // 固定色模式（默认）
        baseColor = isUpper ? colorState.upperTextColor : colorState.lowerTextColor;
        break;
    }

    // 应用呼吸特效（除了渐变色，所有颜色模式都支持呼吸特效）
    bool breatheActive = isUpper ? effectState.upperBreatheActive : effectState.lowerBreatheActive;
    if (breatheActive)
    {
        float phase = isUpper ? effectState.upperBreathePhase : effectState.lowerBreathePhase;
        float brightness = (sin(phase) + 1.0) / 2.0; // 0.0 到 1.0 的正弦波
        brightness = 0.05 + brightness * 0.95;       // 范围从5%-100%，更强烈的呼吸对比度

        // 提取RGB分量（RGB565格式）
        uint8_t r = (baseColor >> 11) & 0x1F; // 5位红色
        uint8_t g = (baseColor >> 5) & 0x3F;  // 6位绿色
        uint8_t b = baseColor & 0x1F;         // 5位蓝色

        // 应用呼吸亮度
        r = (uint8_t)(r * brightness);
        g = (uint8_t)(g * brightness);
        b = (uint8_t)(b * brightness);

        // 重新组合颜色
        return (r << 11) | (g << 5) | b;
    }

    return baseColor;
}

// 释放颜色系统内存
void freeColorSystemMemory()
{
    // 释放特定字符颜色内存
    if (colorState.specificColor.upperCharColors)
    {
        free(colorState.specificColor.upperCharColors);
        colorState.specificColor.upperCharColors = nullptr;
    }
    if (colorState.specificColor.upperHasCustomColor)
    {
        free(colorState.specificColor.upperHasCustomColor);
        colorState.specificColor.upperHasCustomColor = nullptr;
    }
    if (colorState.specificColor.lowerCharColors)
    {
        free(colorState.specificColor.lowerCharColors);
        colorState.specificColor.lowerCharColors = nullptr;
    }
    if (colorState.specificColor.lowerHasCustomColor)
    {
        free(colorState.specificColor.lowerHasCustomColor);
        colorState.specificColor.lowerHasCustomColor = nullptr;
    }

    // 释放随机颜色内存
    if (colorState.randomColor.randomColors)
    {
        free(colorState.randomColor.randomColors);
        colorState.randomColor.randomColors = nullptr;
    }

    // 重置状态
    colorState.specificColor.enabled = false;
    colorState.specificColor.maxUpperChars = 0;
    colorState.specificColor.maxLowerChars = 0;
    colorState.randomColor.enabled = false;
    colorState.randomColor.colorCount = 0;
    colorState.currentColorMode = COLOR_MODE_FIXED;

    Serial.println("颜色系统内存已释放");
}